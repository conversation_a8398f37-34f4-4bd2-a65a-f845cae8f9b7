package Server.Fantasy_Server.controller;

import Server.Fantasy_Server.document.AIRecommendation;
import Server.Fantasy_Server.service.AIRecommendationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/ai-recommendations")
@RequiredArgsConstructor
@SecurityRequirement(name = "bearerAuth")
@Tag(name = "AI Recommendations", description = "AI-powered player recommendations")
public class AIRecommendationController {

    private final AIRecommendationService aiRecommendationService;

    @GetMapping("/gameweek/{gameWeekId}")
    @Operation(summary = "Get gameweek recommendations", description = "Get all AI recommendations for a specific gameweek")
    public ResponseEntity<List<AIRecommendation>> getGameWeekRecommendations(@PathVariable Long gameWeekId) {
        List<AIRecommendation> recommendations = aiRecommendationService.getRecommendationsForGameWeek(gameWeekId);
        return ResponseEntity.ok(recommendations);
    }

    @GetMapping("/gameweek/{gameWeekId}/player-of-week")
    @Operation(summary = "Get Player of the Week", description = "Get Player of the Week recommendations")
    public ResponseEntity<List<AIRecommendation>> getPlayerOfTheWeek(@PathVariable Long gameWeekId) {
        List<AIRecommendation> recommendations = aiRecommendationService.getRecommendationsByType(
                AIRecommendation.RecommendationType.PLAYER_OF_THE_WEEK, gameWeekId);
        return ResponseEntity.ok(recommendations);
    }

    @GetMapping("/gameweek/{gameWeekId}/captain-picks")
    @Operation(summary = "Get Captain Picks", description = "Get captain pick recommendations")
    public ResponseEntity<List<AIRecommendation>> getCaptainPicks(@PathVariable Long gameWeekId) {
        List<AIRecommendation> recommendations = aiRecommendationService.getRecommendationsByType(
                AIRecommendation.RecommendationType.CAPTAIN_PICK, gameWeekId);
        return ResponseEntity.ok(recommendations);
    }

    @GetMapping("/gameweek/{gameWeekId}/transfer-in")
    @Operation(summary = "Get Transfer In recommendations", description = "Get players recommended to transfer in")
    public ResponseEntity<List<AIRecommendation>> getTransferInRecommendations(@PathVariable Long gameWeekId) {
        List<AIRecommendation> recommendations = aiRecommendationService.getRecommendationsByType(
                AIRecommendation.RecommendationType.TRANSFER_IN, gameWeekId);
        return ResponseEntity.ok(recommendations);
    }

    @GetMapping("/gameweek/{gameWeekId}/transfer-out")
    @Operation(summary = "Get Transfer Out recommendations", description = "Get players recommended to transfer out")
    public ResponseEntity<List<AIRecommendation>> getTransferOutRecommendations(@PathVariable Long gameWeekId) {
        List<AIRecommendation> recommendations = aiRecommendationService.getRecommendationsByType(
                AIRecommendation.RecommendationType.TRANSFER_OUT, gameWeekId);
        return ResponseEntity.ok(recommendations);
    }

    @GetMapping("/gameweek/{gameWeekId}/differentials")
    @Operation(summary = "Get Differential Picks", description = "Get low-ownership differential pick recommendations")
    public ResponseEntity<List<AIRecommendation>> getDifferentialPicks(@PathVariable Long gameWeekId) {
        List<AIRecommendation> recommendations = aiRecommendationService.getRecommendationsByType(
                AIRecommendation.RecommendationType.DIFFERENTIAL_PICK, gameWeekId);
        return ResponseEntity.ok(recommendations);
    }

    @PostMapping("/generate")
    @Operation(summary = "Generate recommendations", description = "Manually trigger AI recommendations generation")
    public ResponseEntity<Void> generateRecommendations() {
        aiRecommendationService.generateDailyRecommendations();
        return ResponseEntity.ok().build();
    }
}
