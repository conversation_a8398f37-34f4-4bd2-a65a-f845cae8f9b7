package Server.Fantasy_Server.dto;

import Server.Fantasy_Server.entity.Player;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PlayerDto {

    private Long id;
    private String name;
    private String firstName;
    private String lastName;
    private Player.Position position;
    private TeamDto team;
    private Integer jerseyNumber;
    private LocalDate birthDate;
    private String nationality;
    private Integer heightCm;
    private Integer weightKg;
    private String photoUrl;
    private BigDecimal marketValue;
    private BigDecimal fantasyPrice;
    private Integer totalPoints;
    private BigDecimal formRating;
    private Boolean isActive;
    private Boolean isInjured;
    private String injuryDetails;
}
