# Fantasy Tunisie Backend

A comprehensive Spring Boot backend application for a mobile Fantasy Football game based on the Tunisian Pro League, similar in functionality to the Fantasy Premier League.

## Features

### Core Functionality
- **User Management**: Registration, login with JWT authentication, role-based access control
- **Fantasy Teams**: Create and manage fantasy teams with budget constraints
- **Player Management**: Real-world players with positions, stats, and pricing
- **Transfer System**: Player transfers with budget and rule validation
- **Scoring Engine**: Real-time fantasy points calculation with special bonus rules
- **Leaderboards**: Global, weekly, and monthly rankings

### Advanced Features
- **AI Recommendations**: ML-based player suggestions and predictions
- **External API Integration**: Sync with football APIs for live data
- **Synergy Bonuses**: Special points for same-team combinations
- **Scheduled Jobs**: Automated data synchronization and updates
- **Comprehensive API**: RESTful endpoints with Swagger documentation

## Technology Stack

- **Framework**: Spring Boot 3.5.0
- **Language**: Java 21
- **Databases**: 
  - PostgreSQL (structured data)
  - MongoDB (flexible documents)
- **Security**: Spring Security with JWT
- **Documentation**: OpenAPI 3 (Swagger)
- **Build Tool**: Maven
- **Containerization**: Docker Compose

## Database Design

### PostgreSQL Entities
- Users, Teams, Players, Fixtures
- FantasyTeams, FantasyPlayers, Transfers
- GameWeeks, Leaderboards

### MongoDB Documents
- PlayerStats (flexible stats per fixture)
- AIRecommendations (ML predictions)
- AuditLogs (system tracking)
- PlayerPredictions (performance forecasts)

## Quick Start

### Prerequisites
- Java 21
- Docker and Docker Compose
- Maven 3.6+

### Setup

1. **Clone the repository**
```bash
git clone <repository-url>
cd Fantasy_Server
```

2. **Start databases with Docker Compose**
```bash
docker-compose up -d
```

3. **Configure environment variables** (optional)
```bash
export DB_USERNAME=fantasy_user
export DB_PASSWORD=fantasy_password
export JWT_SECRET=your-secret-key
export FOOTBALL_API_KEY=your-api-key
```

4. **Run the application**
```bash
./mvnw spring-boot:run
```

5. **Access the application**
- API Base URL: http://localhost:8080/api/v1
- Swagger UI: http://localhost:8080/swagger-ui.html
- Health Check: http://localhost:8080/actuator/health

## API Documentation

### Authentication Endpoints
- `POST /auth/register` - Register new user
- `POST /auth/login` - User login
- `POST /auth/refresh-token` - Refresh access token
- `POST /auth/logout` - User logout

### Fantasy Team Management
- `POST /fantasy-teams` - Create fantasy team
- `GET /fantasy-teams/my-team` - Get user's team
- `PUT /fantasy-teams/{id}/captain` - Update captain
- `PUT /fantasy-teams/{id}/lineup` - Update starting lineup

### Player Information
- `GET /players` - Get all players (paginated)
- `GET /players/position/{position}` - Get players by position
- `GET /players/team/{teamId}` - Get team players
- `GET /players/search?name={name}` - Search players

### Transfer System
- `POST /transfers` - Make a transfer
- `GET /transfers/my-transfers` - Get user transfers
- `GET /transfers/gameweek/{id}` - Get gameweek transfers

### Leaderboards
- `GET /leaderboard/overall` - Overall leaderboard
- `GET /leaderboard/gameweek/{id}` - Gameweek leaderboard
- `GET /leaderboard/monthly` - Monthly leaderboard

### AI Recommendations
- `GET /ai-recommendations/gameweek/{id}` - All recommendations
- `GET /ai-recommendations/gameweek/{id}/captain-picks` - Captain suggestions
- `GET /ai-recommendations/gameweek/{id}/transfer-in` - Transfer in suggestions

### Admin Endpoints (ADMIN role required)
- `POST /admin/sync/teams` - Sync teams from API
- `POST /admin/sync/players` - Sync players from API
- `POST /admin/sync/fixtures` - Sync fixtures from API
- `POST /admin/calculate-points/{gameWeekId}` - Calculate points

## Configuration

### Application Properties
Key configuration options in `application.yml`:

```yaml
# Database Configuration
spring:
  datasource:
    url: ************************************************
  data:
    mongodb:
      uri: mongodb://localhost:27017/fantasy_tunisie_stats

# External API Configuration
external-api:
  football:
    base-url: https://api-football-v1.p.rapidapi.com/v3
    api-key: your-api-key
    league-id: 200  # Tunisian League ID

# Fantasy Game Rules
fantasy:
  team:
    max-players: 15
    max-budget: 100000000
  scoring:
    goal:
      goalkeeper: 6
      defender: 6
      midfielder: 5
      forward: 4
```

## Scoring System

### Basic Points
- **Playing**: 1 point (60+ minutes: +1 bonus)
- **Goals**: 4-6 points (position-dependent)
- **Assists**: 3 points
- **Clean Sheet**: 4 points (GK/DEF only)
- **Cards**: Yellow -1, Red -3
- **Penalties**: Save +5, Miss -2

### Special Bonuses
- **Same-team Goal+Assist**: +3 points each
- **Same-team Clean Sheet**: +2 points (GK+DEF)
- **Captain**: Double points
- **Vice Captain**: Double points (if captain doesn't play)

## Development

### Project Structure
```
src/main/java/Server/Fantasy_Server/
├── config/          # Configuration classes
├── controller/      # REST controllers
├── document/        # MongoDB documents
├── dto/            # Data transfer objects
├── entity/         # JPA entities
├── exception/      # Exception handling
├── repository/     # Data repositories
├── security/       # Security configuration
└── service/        # Business logic
```

### Building
```bash
./mvnw clean package
```

### Running Tests
```bash
./mvnw test
```

### Docker Build
```bash
docker build -t fantasy-tunisie-backend .
```

## Deployment

### Environment Variables
- `DB_USERNAME` - PostgreSQL username
- `DB_PASSWORD` - PostgreSQL password
- `MONGO_USERNAME` - MongoDB username
- `MONGO_PASSWORD` - MongoDB password
- `JWT_SECRET` - JWT signing secret
- `FOOTBALL_API_KEY` - External API key

### Production Considerations
- Use environment-specific profiles
- Configure proper logging levels
- Set up monitoring and health checks
- Use secrets management for sensitive data
- Configure CORS for frontend domains

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## License

This project is licensed under the MIT License.
