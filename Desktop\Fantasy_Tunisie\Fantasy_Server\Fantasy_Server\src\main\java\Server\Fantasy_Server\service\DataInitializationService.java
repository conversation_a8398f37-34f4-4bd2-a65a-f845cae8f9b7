package Server.Fantasy_Server.service;

import Server.Fantasy_Server.entity.*;
import Server.Fantasy_Server.repository.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

@Service
@RequiredArgsConstructor
@Slf4j
public class DataInitializationService implements CommandLineRunner {

    private final UserRepository userRepository;
    private final TeamRepository teamRepository;
    private final PlayerRepository playerRepository;
    private final GameWeekRepository gameWeekRepository;
    private final PasswordEncoder passwordEncoder;

    @Override
    @Transactional
    public void run(String... args) {
        if (userRepository.count() == 0) {
            log.info("Initializing sample data...");
            initializeData();
            log.info("Sample data initialization completed");
        }
    }

    private void initializeData() {
        // Create sample users
        createSampleUsers();
        
        // Create sample teams
        createSampleTeams();
        
        // Create sample players
        createSamplePlayers();
        
        // Create sample game weeks
        createSampleGameWeeks();
    }

    private void createSampleUsers() {
        List<User> users = Arrays.asList(
            User.builder()
                .username("admin")
                .email("<EMAIL>")
                .password(passwordEncoder.encode("admin123"))
                .firstName("Admin")
                .lastName("User")
                .role(User.Role.ADMIN)
                .isActive(true)
                .emailVerified(true)
                .build(),
            
            User.builder()
                .username("player1")
                .email("<EMAIL>")
                .password(passwordEncoder.encode("password123"))
                .firstName("Ahmed")
                .lastName("Ben Ali")
                .role(User.Role.PLAYER)
                .isActive(true)
                .emailVerified(true)
                .build(),
                
            User.builder()
                .username("player2")
                .email("<EMAIL>")
                .password(passwordEncoder.encode("password123"))
                .firstName("Mohamed")
                .lastName("Trabelsi")
                .role(User.Role.PLAYER)
                .isActive(true)
                .emailVerified(true)
                .build()
        );

        userRepository.saveAll(users);
        log.info("Created {} sample users", users.size());
    }

    private void createSampleTeams() {
        List<Team> teams = Arrays.asList(
            Team.builder()
                .name("Espérance Sportive de Tunis")
                .shortName("EST")
                .city("Tunis")
                .stadiumName("Stade Hammadi Agrebi")
                .stadiumCapacity(60000)
                .foundedYear(1919)
                .isActive(true)
                .build(),
                
            Team.builder()
                .name("Club Africain")
                .shortName("CA")
                .city("Tunis")
                .stadiumName("Stade Olympique de Radès")
                .stadiumCapacity(65000)
                .foundedYear(1920)
                .isActive(true)
                .build(),
                
            Team.builder()
                .name("Étoile Sportive du Sahel")
                .shortName("ESS")
                .city("Sousse")
                .stadiumName("Stade Olympique de Sousse")
                .stadiumCapacity(28000)
                .foundedYear(1925)
                .isActive(true)
                .build(),
                
            Team.builder()
                .name("Club Sportif Sfaxien")
                .shortName("CSS")
                .city("Sfax")
                .stadiumName("Stade Taïeb Mhiri")
                .stadiumCapacity(22000)
                .foundedYear(1928)
                .isActive(true)
                .build()
        );

        teamRepository.saveAll(teams);
        log.info("Created {} sample teams", teams.size());
    }

    private void createSamplePlayers() {
        List<Team> teams = teamRepository.findAll();
        
        for (Team team : teams) {
            createPlayersForTeam(team);
        }
        
        log.info("Created sample players for all teams");
    }

    private void createPlayersForTeam(Team team) {
        // Create 2 goalkeepers
        for (int i = 1; i <= 2; i++) {
            Player player = Player.builder()
                .name("Goalkeeper " + i + " " + team.getShortName())
                .firstName("GK" + i)
                .lastName(team.getShortName())
                .position(Player.Position.GOALKEEPER)
                .team(team)
                .jerseyNumber(i)
                .birthDate(LocalDate.of(1990 + i, 1, 1))
                .nationality("Tunisia")
                .fantasyPrice(new BigDecimal("4500000"))
                .totalPoints((int) (Math.random() * 50))
                .formRating(new BigDecimal(3.0 + Math.random() * 2))
                .isActive(true)
                .isInjured(false)
                .build();
            playerRepository.save(player);
        }

        // Create 8 defenders
        for (int i = 1; i <= 8; i++) {
            Player player = Player.builder()
                .name("Defender " + i + " " + team.getShortName())
                .firstName("DEF" + i)
                .lastName(team.getShortName())
                .position(Player.Position.DEFENDER)
                .team(team)
                .jerseyNumber(i + 2)
                .birthDate(LocalDate.of(1988 + i, 1, 1))
                .nationality("Tunisia")
                .fantasyPrice(new BigDecimal("5000000"))
                .totalPoints((int) (Math.random() * 80))
                .formRating(new BigDecimal(3.0 + Math.random() * 2))
                .isActive(true)
                .isInjured(Math.random() < 0.1) // 10% chance of injury
                .build();
            playerRepository.save(player);
        }

        // Create 8 midfielders
        for (int i = 1; i <= 8; i++) {
            Player player = Player.builder()
                .name("Midfielder " + i + " " + team.getShortName())
                .firstName("MID" + i)
                .lastName(team.getShortName())
                .position(Player.Position.MIDFIELDER)
                .team(team)
                .jerseyNumber(i + 10)
                .birthDate(LocalDate.of(1985 + i, 1, 1))
                .nationality("Tunisia")
                .fantasyPrice(new BigDecimal("6000000"))
                .totalPoints((int) (Math.random() * 120))
                .formRating(new BigDecimal(3.0 + Math.random() * 2))
                .isActive(true)
                .isInjured(Math.random() < 0.1)
                .build();
            playerRepository.save(player);
        }

        // Create 5 forwards
        for (int i = 1; i <= 5; i++) {
            Player player = Player.builder()
                .name("Forward " + i + " " + team.getShortName())
                .firstName("FWD" + i)
                .lastName(team.getShortName())
                .position(Player.Position.FORWARD)
                .team(team)
                .jerseyNumber(i + 18)
                .birthDate(LocalDate.of(1987 + i, 1, 1))
                .nationality("Tunisia")
                .fantasyPrice(new BigDecimal("7000000"))
                .totalPoints((int) (Math.random() * 150))
                .formRating(new BigDecimal(3.0 + Math.random() * 2))
                .isActive(true)
                .isInjured(Math.random() < 0.1)
                .build();
            playerRepository.save(player);
        }
    }

    private void createSampleGameWeeks() {
        String currentSeason = "2024";
        LocalDateTime now = LocalDateTime.now();
        
        for (int week = 1; week <= 30; week++) {
            LocalDateTime startDate = now.plusDays((week - 1) * 7);
            LocalDateTime endDate = startDate.plusDays(6);
            LocalDateTime deadline = startDate.minusHours(2);
            
            GameWeek gameWeek = GameWeek.builder()
                .weekNumber(week)
                .season(currentSeason)
                .startDate(startDate)
                .endDate(endDate)
                .deadline(deadline)
                .isCurrent(week == 1) // Make first week current
                .isFinished(false)
                .build();
                
            gameWeekRepository.save(gameWeek);
        }
        
        log.info("Created 30 sample game weeks for season {}", currentSeason);
    }
}
