package Server.Fantasy_Server.controller;

import Server.Fantasy_Server.entity.GameWeek;
import Server.Fantasy_Server.service.GameWeekService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/gameweeks")
@RequiredArgsConstructor
@SecurityRequirement(name = "bearerAuth")
@Tag(name = "Game Weeks", description = "Game week management endpoints")
public class GameWeekController {

    private final GameWeekService gameWeekService;

    @GetMapping
    @Operation(summary = "Get all game weeks", description = "Get all game weeks for current season")
    public ResponseEntity<List<GameWeek>> getAllGameWeeks() {
        List<GameWeek> gameWeeks = gameWeekService.getAllGameWeeks();
        return ResponseEntity.ok(gameWeeks);
    }

    @GetMapping("/current")
    @Operation(summary = "Get current game week", description = "Get the current active game week")
    public ResponseEntity<GameWeek> getCurrentGameWeek() {
        GameWeek gameWeek = gameWeekService.getCurrentGameWeek();
        return ResponseEntity.ok(gameWeek);
    }

    @GetMapping("/{gameWeekId}")
    @Operation(summary = "Get game week by ID", description = "Get game week details by ID")
    public ResponseEntity<GameWeek> getGameWeek(@PathVariable Long gameWeekId) {
        GameWeek gameWeek = gameWeekService.getGameWeek(gameWeekId);
        return ResponseEntity.ok(gameWeek);
    }

    @GetMapping("/season/{season}")
    @Operation(summary = "Get game weeks by season", description = "Get all game weeks for a specific season")
    public ResponseEntity<List<GameWeek>> getGameWeeksBySeason(@PathVariable String season) {
        List<GameWeek> gameWeeks = gameWeekService.getGameWeeksBySeason(season);
        return ResponseEntity.ok(gameWeeks);
    }

    @GetMapping("/upcoming")
    @Operation(summary = "Get upcoming game weeks", description = "Get upcoming game weeks")
    public ResponseEntity<List<GameWeek>> getUpcomingGameWeeks() {
        List<GameWeek> gameWeeks = gameWeekService.getUpcomingGameWeeks();
        return ResponseEntity.ok(gameWeeks);
    }
}
