package Server.Fantasy_Server.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class FantasyTeamDto {

    private Long id;
    private String name;
    private Long userId;
    private String username;
    private BigDecimal totalBudget;
    private BigDecimal remainingBudget;
    private Integer totalPoints;
    private Integer gameweekPoints;
    private Integer freeTransfers;
    private Integer totalTransfers;
    private Boolean isActive;
    private LocalDateTime createdAt;
    private List<FantasyPlayerDto> fantasyPlayers;
    private Integer playerCount;
    private Boolean isTeamComplete;
}
