package Server.Fantasy_Server.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

@Entity
@Table(name = "game_weeks")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GameWeek {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "week_number", nullable = false)
    @NotNull(message = "Week number is required")
    private Integer weekNumber;

    @Column(name = "season", nullable = false)
    @NotNull(message = "Season is required")
    private String season;

    @Column(name = "start_date", nullable = false)
    @NotNull(message = "Start date is required")
    private LocalDateTime startDate;

    @Column(name = "end_date", nullable = false)
    @NotNull(message = "End date is required")
    private LocalDateTime endDate;

    @Column(name = "deadline", nullable = false)
    @NotNull(message = "Deadline is required")
    private LocalDateTime deadline;

    @Column(name = "is_current")
    private Boolean isCurrent = false;

    @Column(name = "is_finished")
    private Boolean isFinished = false;

    @Column(name = "created_at")
    private LocalDateTime createdAt;

    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    @OneToMany(mappedBy = "gameWeek", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<Fixture> fixtures;

    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }

    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }

    public boolean isActive() {
        LocalDateTime now = LocalDateTime.now();
        return now.isAfter(startDate) && now.isBefore(endDate);
    }

    public boolean isDeadlinePassed() {
        return LocalDateTime.now().isAfter(deadline);
    }
}
