package Server.Fantasy_Server.controller;

import Server.Fantasy_Server.dto.PlayerDto;
import Server.Fantasy_Server.entity.Player;
import Server.Fantasy_Server.service.PlayerService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;

@RestController
@RequestMapping("/players")
@RequiredArgsConstructor
@SecurityRequirement(name = "bearerAuth")
@Tag(name = "Players", description = "Player information and statistics endpoints")
public class PlayerController {

    private final PlayerService playerService;

    @GetMapping
    @Operation(summary = "Get all players", description = "Get paginated list of all active players")
    public ResponseEntity<Page<PlayerDto>> getAllPlayers(Pageable pageable) {
        Page<PlayerDto> players = playerService.getAllPlayers(pageable);
        return ResponseEntity.ok(players);
    }

    @GetMapping("/{playerId}")
    @Operation(summary = "Get player by ID", description = "Get detailed player information by ID")
    public ResponseEntity<PlayerDto> getPlayer(@PathVariable Long playerId) {
        PlayerDto player = playerService.getPlayer(playerId);
        return ResponseEntity.ok(player);
    }

    @GetMapping("/position/{position}")
    @Operation(summary = "Get players by position", description = "Get all players by position")
    public ResponseEntity<List<PlayerDto>> getPlayersByPosition(@PathVariable Player.Position position) {
        List<PlayerDto> players = playerService.getPlayersByPosition(position);
        return ResponseEntity.ok(players);
    }

    @GetMapping("/team/{teamId}")
    @Operation(summary = "Get players by team", description = "Get all players for a specific team")
    public ResponseEntity<List<PlayerDto>> getPlayersByTeam(@PathVariable Long teamId) {
        List<PlayerDto> players = playerService.getPlayersByTeam(teamId);
        return ResponseEntity.ok(players);
    }

    @GetMapping("/search")
    @Operation(summary = "Search players", description = "Search players by name")
    public ResponseEntity<List<PlayerDto>> searchPlayers(@RequestParam String name) {
        List<PlayerDto> players = playerService.searchPlayersByName(name);
        return ResponseEntity.ok(players);
    }

    @GetMapping("/price-range")
    @Operation(summary = "Get players by price range", description = "Get players within specified price range")
    public ResponseEntity<List<PlayerDto>> getPlayersByPriceRange(
            @RequestParam BigDecimal minPrice,
            @RequestParam BigDecimal maxPrice
    ) {
        List<PlayerDto> players = playerService.getPlayersByPriceRange(minPrice, maxPrice);
        return ResponseEntity.ok(players);
    }

    @GetMapping("/top-performers")
    @Operation(summary = "Get top performing players", description = "Get players with highest fantasy points")
    public ResponseEntity<List<PlayerDto>> getTopPerformers(
            @RequestParam(defaultValue = "50") Integer minPoints
    ) {
        List<PlayerDto> players = playerService.getTopPerformers(minPoints);
        return ResponseEntity.ok(players);
    }

    @GetMapping("/popular")
    @Operation(summary = "Get popular players", description = "Get most owned players")
    public ResponseEntity<List<PlayerDto>> getPopularPlayers(
            @RequestParam(defaultValue = "10") Long minOwnership
    ) {
        List<PlayerDto> players = playerService.getPopularPlayers(minOwnership);
        return ResponseEntity.ok(players);
    }

    @GetMapping("/injured")
    @Operation(summary = "Get injured players", description = "Get all currently injured players")
    public ResponseEntity<List<PlayerDto>> getInjuredPlayers() {
        List<PlayerDto> players = playerService.getInjuredPlayers();
        return ResponseEntity.ok(players);
    }
}
