package Server.Fantasy_Server.controller;

import Server.Fantasy_Server.service.ExternalApiService;
import Server.Fantasy_Server.service.ScoringService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/admin")
@RequiredArgsConstructor
@SecurityRequirement(name = "bearerAuth")
@Tag(name = "Admin", description = "Administrative endpoints")
@PreAuthorize("hasRole('ADMIN')")
public class AdminController {

    private final ExternalApiService externalApiService;
    private final ScoringService scoringService;

    @PostMapping("/sync/teams")
    @Operation(summary = "Sync teams", description = "Synchronize teams from external API")
    public ResponseEntity<String> syncTeams() {
        externalApiService.syncTeams();
        return ResponseEntity.ok("Teams synchronization completed");
    }

    @PostMapping("/sync/players")
    @Operation(summary = "Sync players", description = "Synchronize players from external API")
    public ResponseEntity<String> syncPlayers() {
        externalApiService.syncPlayers();
        return ResponseEntity.ok("Players synchronization completed");
    }

    @PostMapping("/sync/fixtures")
    @Operation(summary = "Sync fixtures", description = "Synchronize fixtures from external API")
    public ResponseEntity<String> syncFixtures() {
        externalApiService.syncFixtures();
        return ResponseEntity.ok("Fixtures synchronization completed");
    }

    @PostMapping("/sync/stats/{fixtureId}")
    @Operation(summary = "Sync player stats", description = "Synchronize player stats for a specific fixture")
    public ResponseEntity<String> syncPlayerStats(@PathVariable Long fixtureId) {
        externalApiService.syncPlayerStats(fixtureId);
        return ResponseEntity.ok("Player stats synchronization completed for fixture: " + fixtureId);
    }

    @PostMapping("/calculate-points/{gameWeekId}")
    @Operation(summary = "Calculate points", description = "Calculate fantasy points for a game week")
    public ResponseEntity<String> calculatePoints(@PathVariable Long gameWeekId) {
        scoringService.calculateGameWeekPoints(gameWeekId);
        return ResponseEntity.ok("Points calculation completed for game week: " + gameWeekId);
    }
}
