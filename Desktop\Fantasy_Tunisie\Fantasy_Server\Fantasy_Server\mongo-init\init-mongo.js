// MongoDB initialization script for Fantasy Tunisie
print('Starting MongoDB initialization for Fantasy Tunisie...');

// Switch to the fantasy_tunisie_stats database
db = db.getSiblingDB('fantasy_tunisie_stats');

// Create a user for the application
db.createUser({
  user: 'fantasy_user',
  pwd: 'fantasy_password',
  roles: [
    {
      role: 'readWrite',
      db: 'fantasy_tunisie_stats'
    }
  ]
});

// Create collections with indexes
print('Creating collections and indexes...');

// Player Stats collection
db.createCollection('player_stats');
db.player_stats.createIndex({ "player_id": 1 });
db.player_stats.createIndex({ "fixture_id": 1 });
db.player_stats.createIndex({ "game_week_id": 1 });
db.player_stats.createIndex({ "team_id": 1 });
db.player_stats.createIndex({ "season": 1 });
db.player_stats.createIndex({ "player_id": 1, "fixture_id": 1 }, { unique: true });

// AI Recommendations collection
db.createCollection('ai_recommendations');
db.ai_recommendations.createIndex({ "player_id": 1 });
db.ai_recommendations.createIndex({ "game_week_id": 1 });
db.ai_recommendations.createIndex({ "recommendation_type": 1 });
db.ai_recommendations.createIndex({ "is_active": 1 });
db.ai_recommendations.createIndex({ "expires_at": 1 });

// Audit Logs collection
db.createCollection('audit_logs');
db.audit_logs.createIndex({ "user_id": 1 });
db.audit_logs.createIndex({ "timestamp": -1 });
db.audit_logs.createIndex({ "action": 1 });
db.audit_logs.createIndex({ "entity_type": 1 });

// Player Predictions collection
db.createCollection('player_predictions');
db.player_predictions.createIndex({ "player_id": 1 });
db.player_predictions.createIndex({ "fixture_id": 1 });
db.player_predictions.createIndex({ "game_week_id": 1 });
db.player_predictions.createIndex({ "is_evaluated": 1 });

// Insert sample data
print('Inserting sample data...');

// Sample player stats
db.player_stats.insertMany([
  {
    player_id: 1,
    fixture_id: 1,
    team_id: 1,
    game_week_id: 1,
    season: "2024",
    minutes_played: 90,
    goals: 2,
    assists: 1,
    yellow_cards: 0,
    red_cards: 0,
    clean_sheet: false,
    fantasy_points: 15,
    created_at: new Date(),
    updated_at: new Date()
  },
  {
    player_id: 2,
    fixture_id: 1,
    team_id: 1,
    game_week_id: 1,
    season: "2024",
    minutes_played: 90,
    goals: 0,
    assists: 2,
    yellow_cards: 1,
    red_cards: 0,
    clean_sheet: true,
    fantasy_points: 8,
    created_at: new Date(),
    updated_at: new Date()
  }
]);

// Sample AI recommendations
db.ai_recommendations.insertMany([
  {
    player_id: 1,
    game_week_id: 1,
    season: "2024",
    recommendation_type: "PLAYER_OF_THE_WEEK",
    confidence_score: 0.85,
    predicted_points: 12.5,
    form_rating: 4.2,
    recommendation_reason: "Excellent recent form with 3 goals in last 2 games",
    algorithm_version: "1.0",
    created_at: new Date(),
    expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
    is_active: true
  },
  {
    player_id: 3,
    game_week_id: 1,
    season: "2024",
    recommendation_type: "CAPTAIN_PICK",
    confidence_score: 0.78,
    predicted_points: 10.0,
    form_rating: 3.8,
    recommendation_reason: "Consistent performer with good fixture",
    algorithm_version: "1.0",
    created_at: new Date(),
    expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
    is_active: true
  }
]);

print('MongoDB initialization completed successfully!');
print('Database: fantasy_tunisie_stats');
print('User: fantasy_user');
print('Collections created: player_stats, ai_recommendations, audit_logs, player_predictions');
