package Server.Fantasy_Server.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CreateFantasyTeamRequest {

    @NotBlank(message = "Team name is required")
    @Size(min = 3, max = 50, message = "Team name must be between 3 and 50 characters")
    private String name;

    @NotNull(message = "Player IDs are required")
    @Size(min = 15, max = 15, message = "Team must have exactly 15 players")
    private List<Long> playerIds;

    @NotNull(message = "Captain ID is required")
    private Long captainId;

    @NotNull(message = "Vice captain ID is required")
    private Long viceCaptainId;

    @NotNull(message = "Starting lineup is required")
    @Size(min = 11, max = 11, message = "Starting lineup must have exactly 11 players")
    private List<Long> startingLineup;
}
