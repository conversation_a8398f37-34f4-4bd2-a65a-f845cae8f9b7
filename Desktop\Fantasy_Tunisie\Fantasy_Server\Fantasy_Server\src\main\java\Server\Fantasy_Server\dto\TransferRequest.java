package Server.Fantasy_Server.dto;

import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TransferRequest {

    @NotNull(message = "Player in ID is required")
    private Long playerInId;

    @NotNull(message = "Player out ID is required")
    private Long playerOutId;

    @NotNull(message = "Fantasy team ID is required")
    private Long fantasyTeamId;
}
