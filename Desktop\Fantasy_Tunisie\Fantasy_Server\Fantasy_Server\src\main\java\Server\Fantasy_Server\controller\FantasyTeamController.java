package Server.Fantasy_Server.controller;

import Server.Fantasy_Server.dto.*;
import Server.Fantasy_Server.service.FantasyTeamService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/fantasy-teams")
@RequiredArgsConstructor
@SecurityRequirement(name = "bearerAuth")
@Tag(name = "Fantasy Teams", description = "Fantasy team management endpoints")
public class FantasyTeamController {

    private final FantasyTeamService fantasyTeamService;

    @PostMapping
    @Operation(summary = "Create fantasy team", description = "Create a new fantasy team for the authenticated user")
    public ResponseEntity<FantasyTeamDto> createFantasyTeam(
            @Valid @RequestBody CreateFantasyTeamRequest request,
            Authentication authentication
    ) {
        FantasyTeamDto team = fantasyTeamService.createFantasyTeam(request, authentication.getName());
        return ResponseEntity.status(HttpStatus.CREATED).body(team);
    }

    @GetMapping("/my-team")
    @Operation(summary = "Get user's fantasy team", description = "Get the authenticated user's active fantasy team")
    public ResponseEntity<FantasyTeamDto> getMyFantasyTeam(Authentication authentication) {
        FantasyTeamDto team = fantasyTeamService.getUserFantasyTeam(authentication.getName());
        return ResponseEntity.ok(team);
    }

    @GetMapping("/{teamId}")
    @Operation(summary = "Get fantasy team by ID", description = "Get fantasy team details by team ID")
    public ResponseEntity<FantasyTeamDto> getFantasyTeam(@PathVariable Long teamId) {
        FantasyTeamDto team = fantasyTeamService.getFantasyTeam(teamId);
        return ResponseEntity.ok(team);
    }

    @GetMapping
    @Operation(summary = "Get all fantasy teams", description = "Get all active fantasy teams")
    public ResponseEntity<List<FantasyTeamDto>> getAllFantasyTeams() {
        List<FantasyTeamDto> teams = fantasyTeamService.getAllFantasyTeams();
        return ResponseEntity.ok(teams);
    }

    @PutMapping("/{teamId}/captain")
    @Operation(summary = "Update team captain", description = "Update captain and vice captain for the team")
    public ResponseEntity<FantasyTeamDto> updateCaptain(
            @PathVariable Long teamId,
            @RequestParam Long captainId,
            @RequestParam Long viceCaptainId,
            Authentication authentication
    ) {
        FantasyTeamDto team = fantasyTeamService.updateCaptain(teamId, captainId, viceCaptainId, authentication.getName());
        return ResponseEntity.ok(team);
    }

    @PutMapping("/{teamId}/lineup")
    @Operation(summary = "Update starting lineup", description = "Update the starting 11 players for the team")
    public ResponseEntity<FantasyTeamDto> updateStartingLineup(
            @PathVariable Long teamId,
            @RequestBody List<Long> startingLineup,
            Authentication authentication
    ) {
        FantasyTeamDto team = fantasyTeamService.updateStartingLineup(teamId, startingLineup, authentication.getName());
        return ResponseEntity.ok(team);
    }
}
