package Server.Fantasy_Server.dto;

import Server.Fantasy_Server.entity.Leaderboard;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LeaderboardDto {

    private Long id;
    private String username;
    private String fantasyTeamName;
    private Integer totalPoints;
    private Integer gameweekPoints;
    private Integer overallRank;
    private Integer gameweekRank;
    private Integer previousRank;
    private Integer rankChange;
    private Leaderboard.LeaderboardType type;
}
