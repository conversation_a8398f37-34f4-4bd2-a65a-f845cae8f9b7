package Server.Fantasy_Server.repository;

import Server.Fantasy_Server.entity.FantasyTeam;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface FantasyTeamRepository extends JpaRepository<FantasyTeam, Long> {

    List<FantasyTeam> findByUserId(Long userId);

    Optional<FantasyTeam> findByUserIdAndIsActiveTrue(Long userId);

    List<FantasyTeam> findByIsActiveTrue();

    @Query("SELECT ft FROM FantasyTeam ft WHERE ft.totalPoints >= :minPoints ORDER BY ft.totalPoints DESC")
    List<FantasyTeam> findTopTeams(@Param("minPoints") Integer minPoints);

    @Query("SELECT ft FROM FantasyTeam ft ORDER BY ft.totalPoints DESC")
    List<FantasyTeam> findAllOrderByTotalPointsDesc();

    @Query("SELECT ft FROM FantasyTeam ft ORDER BY ft.gameweekPoints DESC")
    List<FantasyTeam> findAllOrderByGameweekPointsDesc();

    @Query("SELECT ft FROM FantasyTeam ft WHERE ft.name LIKE %:name%")
    List<FantasyTeam> findByNameContaining(@Param("name") String name);

    @Query("SELECT COUNT(fp) FROM FantasyTeam ft JOIN ft.fantasyPlayers fp WHERE ft.id = :teamId")
    long countPlayersByTeamId(@Param("teamId") Long teamId);

    @Query("SELECT ft FROM FantasyTeam ft JOIN ft.fantasyPlayers fp WHERE fp.player.id = :playerId")
    List<FantasyTeam> findTeamsWithPlayer(@Param("playerId") Long playerId);

    @Query("SELECT AVG(ft.totalPoints) FROM FantasyTeam ft WHERE ft.isActive = true")
    Double getAverageTotalPoints();

    @Query("SELECT ft FROM FantasyTeam ft WHERE SIZE(ft.fantasyPlayers) = 15 AND ft.isActive = true")
    List<FantasyTeam> findCompleteTeams();
}
