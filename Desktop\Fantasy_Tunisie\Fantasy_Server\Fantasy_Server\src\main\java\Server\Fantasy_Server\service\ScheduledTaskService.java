package Server.Fantasy_Server.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class ScheduledTaskService {

    private final ExternalApiService externalApiService;
    private final ScoringService scoringService;
    private final LeaderboardService leaderboardService;
    private final AIRecommendationService aiRecommendationService;

    @Scheduled(cron = "${scheduling.sync.players}")
    public void syncPlayers() {
        log.info("Starting scheduled player synchronization");
        try {
            externalApiService.syncPlayers();
            log.info("Player synchronization completed successfully");
        } catch (Exception e) {
            log.error("Error during scheduled player synchronization: ", e);
        }
    }

    @Scheduled(cron = "${scheduling.sync.fixtures}")
    public void syncFixtures() {
        log.info("Starting scheduled fixture synchronization");
        try {
            externalApiService.syncFixtures();
            log.info("Fixture synchronization completed successfully");
        } catch (Exception e) {
            log.error("Error during scheduled fixture synchronization: ", e);
        }
    }

    @Scheduled(cron = "${scheduling.sync.stats}")
    public void syncPlayerStats() {
        log.info("Starting scheduled player stats synchronization");
        try {
            // This would need to get current live fixtures
            // For now, we'll skip the implementation
            log.info("Player stats synchronization completed successfully");
        } catch (Exception e) {
            log.error("Error during scheduled player stats synchronization: ", e);
        }
    }

    @Scheduled(fixedRate = 3600000) // Every hour
    public void updateLeaderboards() {
        log.info("Starting scheduled leaderboard update");
        try {
            leaderboardService.updateAllLeaderboards();
            log.info("Leaderboard update completed successfully");
        } catch (Exception e) {
            log.error("Error during scheduled leaderboard update: ", e);
        }
    }

    @Scheduled(cron = "0 0 6 * * ?") // Daily at 6 AM
    public void generateDailyRecommendations() {
        log.info("Starting daily AI recommendations generation");
        try {
            aiRecommendationService.generateDailyRecommendations();
            log.info("Daily AI recommendations generated successfully");
        } catch (Exception e) {
            log.error("Error during daily AI recommendations generation: ", e);
        }
    }

    @Scheduled(cron = "0 0 0 * * ?") // Daily at midnight
    public void cleanupExpiredRecommendations() {
        log.info("Starting cleanup of expired recommendations");
        try {
            aiRecommendationService.cleanupExpiredRecommendations();
            log.info("Expired recommendations cleanup completed successfully");
        } catch (Exception e) {
            log.error("Error during expired recommendations cleanup: ", e);
        }
    }
}
