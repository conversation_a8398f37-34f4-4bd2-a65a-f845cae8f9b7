package Server.Fantasy_Server.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Entity
@Table(name = "fixtures")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Fixture {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "home_team_id", nullable = false)
    @NotNull(message = "Home team is required")
    private Team homeTeam;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "away_team_id", nullable = false)
    @NotNull(message = "Away team is required")
    private Team awayTeam;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "game_week_id", nullable = false)
    @NotNull(message = "Game week is required")
    private GameWeek gameWeek;

    @Column(name = "kick_off_time", nullable = false)
    @NotNull(message = "Kick off time is required")
    private LocalDateTime kickOffTime;

    @Column(name = "home_score")
    private Integer homeScore;

    @Column(name = "away_score")
    private Integer awayScore;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private FixtureStatus status = FixtureStatus.SCHEDULED;

    @Column(name = "external_api_id", unique = true)
    private Long externalApiId;

    @Column(name = "venue")
    private String venue;

    @Column(name = "referee")
    private String referee;

    @Column(name = "attendance")
    private Integer attendance;

    @Column(name = "weather_condition")
    private String weatherCondition;

    @Column(name = "created_at")
    private LocalDateTime createdAt;

    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }

    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }

    public boolean isFinished() {
        return status == FixtureStatus.FINISHED;
    }

    public boolean isLive() {
        return status == FixtureStatus.LIVE;
    }

    public boolean hasStarted() {
        return status == FixtureStatus.LIVE || status == FixtureStatus.FINISHED;
    }

    public enum FixtureStatus {
        SCHEDULED,
        LIVE,
        HALFTIME,
        FINISHED,
        POSTPONED,
        CANCELLED,
        SUSPENDED
    }
}
