package Server.Fantasy_Server.repository;

import Server.Fantasy_Server.entity.Fixture;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface FixtureRepository extends JpaRepository<Fixture, Long> {

    Optional<Fixture> findByExternalApiId(Long externalApiId);

    List<Fixture> findByGameWeekId(Long gameWeekId);

    List<Fixture> findByStatus(Fixture.FixtureStatus status);

    List<Fixture> findByHomeTeamId(Long teamId);

    List<Fixture> findByAwayTeamId(Long teamId);

    @Query("SELECT f FROM Fixture f WHERE f.homeTeam.id = :teamId OR f.awayTeam.id = :teamId")
    List<Fixture> findByTeamId(@Param("teamId") Long teamId);

    @Query("SELECT f FROM Fixture f WHERE f.kickOffTime BETWEEN :startDate AND :endDate")
    List<Fixture> findByKickOffTimeBetween(@Param("startDate") LocalDateTime startDate, 
                                          @Param("endDate") LocalDateTime endDate);

    @Query("SELECT f FROM Fixture f WHERE f.kickOffTime >= :date ORDER BY f.kickOffTime ASC")
    List<Fixture> findUpcomingFixtures(@Param("date") LocalDateTime date);

    @Query("SELECT f FROM Fixture f WHERE f.kickOffTime < :date AND f.status = 'FINISHED' ORDER BY f.kickOffTime DESC")
    List<Fixture> findCompletedFixtures(@Param("date") LocalDateTime date);

    @Query("SELECT f FROM Fixture f WHERE f.status IN ('LIVE', 'HALFTIME') ORDER BY f.kickOffTime ASC")
    List<Fixture> findLiveFixtures();

    @Query("SELECT f FROM Fixture f WHERE f.gameWeek.id = :gameWeekId AND f.status = 'FINISHED'")
    List<Fixture> findFinishedFixturesByGameWeek(@Param("gameWeekId") Long gameWeekId);

    @Query("SELECT f FROM Fixture f WHERE f.gameWeek.id = :gameWeekId ORDER BY f.kickOffTime ASC")
    List<Fixture> findByGameWeekIdOrderByKickOffTime(@Param("gameWeekId") Long gameWeekId);

    @Query("SELECT COUNT(f) FROM Fixture f WHERE f.gameWeek.id = :gameWeekId AND f.status = 'FINISHED'")
    long countFinishedFixturesByGameWeek(@Param("gameWeekId") Long gameWeekId);

    boolean existsByExternalApiId(Long externalApiId);

    @Query("SELECT f FROM Fixture f WHERE f.homeTeam.id = :homeTeamId AND f.awayTeam.id = :awayTeamId AND f.gameWeek.id = :gameWeekId")
    Optional<Fixture> findByTeamsAndGameWeek(@Param("homeTeamId") Long homeTeamId, 
                                           @Param("awayTeamId") Long awayTeamId, 
                                           @Param("gameWeekId") Long gameWeekId);
}
