version: '3.8'

services:
  postgres:
    image: postgres:15-alpine
    container_name: fantasy-postgres
    environment:
      POSTGRES_DB: fantasy_tunisie
      POSTGRES_USER: fantasy_user
      POSTGRES_PASSWORD: fantasy_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-scripts:/docker-entrypoint-initdb.d
    networks:
      - fantasy-network

  mongodb:
    image: mongo:7.0
    container_name: fantasy-mongo
    environment:
      MONGO_INITDB_ROOT_USERNAME: fantasy_user
      MONGO_INITDB_ROOT_PASSWORD: fantasy_password
      MONGO_INITDB_DATABASE: fantasy_tunisie_stats
    ports:
      - "27017:27017"
    volumes:
      - mongo_data:/data/db
      - ./mongo-init:/docker-entrypoint-initdb.d
    networks:
      - fantasy-network
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    container_name: fantasy-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - fantasy-network
    command: redis-server --appendonly yes

volumes:
  postgres_data:
  mongo_data:
  redis_data:

networks:
  fantasy-network:
    driver: bridge
