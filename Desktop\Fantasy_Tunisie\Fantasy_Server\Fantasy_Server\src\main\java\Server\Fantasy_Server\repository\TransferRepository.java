package Server.Fantasy_Server.repository;

import Server.Fantasy_Server.entity.Transfer;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface TransferRepository extends JpaRepository<Transfer, Long> {

    List<Transfer> findByUserId(Long userId);

    List<Transfer> findByFantasyTeamId(Long fantasyTeamId);

    List<Transfer> findByGameWeekId(Long gameWeekId);

    List<Transfer> findByStatus(Transfer.TransferStatus status);

    List<Transfer> findByPlayerInId(Long playerId);

    List<Transfer> findByPlayerOutId(Long playerId);

    @Query("SELECT t FROM Transfer t WHERE t.user.id = :userId AND t.gameWeek.id = :gameWeekId")
    List<Transfer> findByUserIdAndGameWeekId(@Param("userId") Long userId, @Param("gameWeekId") Long gameWeekId);

    @Query("SELECT t FROM Transfer t WHERE t.status = 'PENDING' AND t.createdAt < :cutoffTime")
    List<Transfer> findPendingTransfersOlderThan(@Param("cutoffTime") LocalDateTime cutoffTime);

    @Query("SELECT COUNT(t) FROM Transfer t WHERE t.user.id = :userId AND t.gameWeek.id = :gameWeekId")
    long countByUserIdAndGameWeekId(@Param("userId") Long userId, @Param("gameWeekId") Long gameWeekId);

    @Query("SELECT t FROM Transfer t WHERE t.isFreeTransfer = true AND t.gameWeek.id = :gameWeekId")
    List<Transfer> findFreeTransfersByGameWeek(@Param("gameWeekId") Long gameWeekId);

    @Query("SELECT t FROM Transfer t WHERE t.isFreeTransfer = false AND t.gameWeek.id = :gameWeekId")
    List<Transfer> findPaidTransfersByGameWeek(@Param("gameWeekId") Long gameWeekId);

    @Query("SELECT t FROM Transfer t ORDER BY t.createdAt DESC")
    List<Transfer> findAllOrderByCreatedAtDesc();

    @Query("SELECT COUNT(t) FROM Transfer t WHERE t.playerIn.id = :playerId")
    long countTransfersInForPlayer(@Param("playerId") Long playerId);

    @Query("SELECT COUNT(t) FROM Transfer t WHERE t.playerOut.id = :playerId")
    long countTransfersOutForPlayer(@Param("playerId") Long playerId);
}
