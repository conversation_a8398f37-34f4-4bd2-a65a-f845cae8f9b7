spring:
  application:
    name: fantasy-tunisie-backend
  
  # Database Configuration - Development Profile
  profiles:
    active: ${SPRING_PROFILES_ACTIVE:dev}

  # H2 Configuration (for development without <PERSON><PERSON>)
  datasource:
    url: jdbc:h2:mem:fantasy_tunisie;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    username: sa
    password:
    driver-class-name: org.h2.Driver

  # JPA Configuration
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: ${SHOW_SQL:true}
    properties:
      hibernate:
        dialect: org.hibernate.dialect.H2Dialect
        format_sql: true

  # H2 Console (for development)
  h2:
    console:
      enabled: true
      path: /h2-console

  # MongoDB Configuration (disabled for testing)
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration
      - org.springframework.boot.autoconfigure.data.mongo.MongoDataAutoConfiguration
  
  # Security Configuration
  security:
    jwt:
      secret-key: ${JWT_SECRET:mySecretKey123456789012345678901234567890}
      expiration: ${JWT_EXPIRATION:86400000} # 24 hours in milliseconds
      refresh-expiration: ${JWT_REFRESH_EXPIRATION:604800000} # 7 days in milliseconds

# Server Configuration
server:
  port: ${SERVER_PORT:8080}
  servlet:
    context-path: /api/v1

# External API Configuration
external-api:
  football:
    base-url: ${FOOTBALL_API_URL:https://api-football-v1.p.rapidapi.com/v3}
    api-key: ${FOOTBALL_API_KEY:your-api-key-here}
    host: ${FOOTBALL_API_HOST:api-football-v1.p.rapidapi.com}
    league-id: ${TUNISIAN_LEAGUE_ID:200} # Tunisian Ligue Professionnelle 1
    season: ${CURRENT_SEASON:2024}

# Fantasy Game Configuration
fantasy:
  team:
    max-players: 15
    max-budget: 100000000 # 100 million in smallest currency unit
    formation:
      min-goalkeepers: 2
      max-goalkeepers: 2
      min-defenders: 5
      max-defenders: 5
      min-midfielders: 5
      max-midfielders: 5
      min-forwards: 3
      max-forwards: 3
  
  scoring:
    goal:
      goalkeeper: 6
      defender: 6
      midfielder: 5
      forward: 4
    assist: 3
    clean-sheet:
      goalkeeper: 4
      defender: 4
    yellow-card: -1
    red-card: -3
    own-goal: -2
    penalty-miss: -2
    penalty-save: 5
    bonus:
      same-team-goal-assist: 3
      same-team-clean-sheet: 2
  
  transfers:
    max-per-gameweek: 1
    max-free-transfers: 1
    transfer-cost: 4 # points deducted per transfer

# Scheduling Configuration
scheduling:
  sync:
    players: "0 0 2 * * ?" # Daily at 2 AM
    fixtures: "0 0 1 * * ?" # Daily at 1 AM
    stats: "0 */30 * * * ?" # Every 30 minutes during match days
  
# Logging Configuration
logging:
  level:
    com.fantasy.tunisie: ${LOG_LEVEL:INFO}
    org.springframework.security: DEBUG
    org.hibernate.SQL: ${SQL_LOG_LEVEL:WARN}
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# Swagger/OpenAPI Configuration
springdoc:
  api-docs:
    path: /api-docs
  swagger-ui:
    path: /swagger-ui.html
    operationsSorter: method
    tagsSorter: alpha

# Management/Actuator Configuration
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: when-authorized

# Cache Configuration
spring.cache:
  type: simple
  cache-names: players,teams,fixtures,leaderboard

# Validation Configuration
spring.mvc:
  throw-exception-if-no-handler-found: true
spring.web:
  resources:
    add-mappings: false
