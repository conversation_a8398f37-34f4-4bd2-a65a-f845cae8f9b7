package Server.Fantasy_Server.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class FantasyPlayerDto {

    private Long id;
    private Long fantasyTeamId;
    private PlayerDto player;
    private BigDecimal purchasePrice;
    private Boolean isCaptain;
    private Boolean isViceCaptain;
    private Boolean isStarter;
    private Integer gameweekPoints;
    private Integer totalPoints;
    private Integer effectivePoints;
}
