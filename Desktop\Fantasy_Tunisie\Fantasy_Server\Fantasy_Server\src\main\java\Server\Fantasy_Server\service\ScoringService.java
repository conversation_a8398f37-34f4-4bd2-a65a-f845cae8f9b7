package Server.Fantasy_Server.service;

import Server.Fantasy_Server.document.PlayerStat;
import Server.Fantasy_Server.entity.*;
import Server.Fantasy_Server.repository.*;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ScoringService {

    private final MockPlayerStatService mockPlayerStatService;
    private final FantasyPlayerRepository fantasyPlayerRepository;
    private final FantasyTeamRepository fantasyTeamRepository;
    private final PlayerRepository playerRepository;
    private final TeamRepository teamRepository;

    public ScoringService(MockPlayerStatService mockPlayerStatService,
                         FantasyPlayerRepository fantasyPlayerRepository,
                         FantasyTeamRepository fantasyTeamRepository,
                         PlayerRepository playerRepository,
                         TeamRepository teamRepository) {
        this.mockPlayerStatService = mockPlayerStatService;
        this.fantasyPlayerRepository = fantasyPlayerRepository;
        this.fantasyTeamRepository = fantasyTeamRepository;
        this.playerRepository = playerRepository;
        this.teamRepository = teamRepository;
    }

    @Transactional
    public void calculateGameWeekPoints(Long gameWeekId) {
        log.info("Starting points calculation for game week: {}", gameWeekId);

        List<PlayerStat> gameWeekStats = mockPlayerStatService.findByGameWeekId(gameWeekId);
        
        // Calculate basic fantasy points for each player
        for (PlayerStat stat : gameWeekStats) {
            calculateBasicFantasyPoints(stat);
        }

        // Apply synergy bonuses
        applySynergyBonuses(gameWeekStats);

        // Save updated stats
        mockPlayerStatService.saveAll(gameWeekStats);

        // Update fantasy team points
        updateFantasyTeamPoints(gameWeekId);

        log.info("Completed points calculation for game week: {}", gameWeekId);
    }

    private void calculateBasicFantasyPoints(PlayerStat stat) {
        Player player = playerRepository.findById(stat.getPlayerId())
                .orElseThrow(() -> new RuntimeException("Player not found: " + stat.getPlayerId()));

        int points = 0;

        // Basic points for playing
        if (stat.getMinutesPlayed() > 0) {
            points += 1;
        }
        if (stat.getMinutesPlayed() >= 60) {
            points += 1; // Additional point for playing 60+ minutes
        }

        // Goals (position-based scoring)
        points += stat.getGoals() * getGoalPoints(player.getPosition());

        // Assists
        points += stat.getAssists() * 3;

        // Clean sheet (GK and DEF only)
        if (stat.getCleanSheet() && isDefensivePosition(player.getPosition())) {
            points += 4;
        }

        // Cards
        points -= stat.getYellowCards() * 1;
        points -= stat.getRedCards() * 3;

        // Own goals
        points -= stat.getOwnGoals() * 2;

        // Penalties
        points += stat.getPenaltiesScored() * getGoalPoints(player.getPosition());
        points -= stat.getPenaltiesMissed() * 2;
        points += stat.getPenaltiesSaved() * 5;

        stat.setFantasyPoints(points);
    }

    private void applySynergyBonuses(List<PlayerStat> gameWeekStats) {
        // Group stats by fixture
        Map<Long, List<PlayerStat>> statsByFixture = gameWeekStats.stream()
                .collect(Collectors.groupingBy(PlayerStat::getFixtureId));

        for (List<PlayerStat> fixtureStats : statsByFixture.values()) {
            applySameTeamGoalAssistBonus(fixtureStats);
            applySameTeamCleanSheetBonus(fixtureStats);
        }
    }

    private void applySameTeamGoalAssistBonus(List<PlayerStat> fixtureStats) {
        // Group by team
        Map<Long, List<PlayerStat>> statsByTeam = fixtureStats.stream()
                .collect(Collectors.groupingBy(PlayerStat::getTeamId));

        for (List<PlayerStat> teamStats : statsByTeam.values()) {
            // Find players who scored and assisted in the same fixture
            List<PlayerStat> goalScorers = teamStats.stream()
                    .filter(stat -> stat.getGoals() > 0)
                    .collect(Collectors.toList());

            List<PlayerStat> assistProviders = teamStats.stream()
                    .filter(stat -> stat.getAssists() > 0)
                    .collect(Collectors.toList());

            // Apply bonus if there are both goals and assists from same team
            if (!goalScorers.isEmpty() && !assistProviders.isEmpty()) {
                for (PlayerStat scorer : goalScorers) {
                    for (PlayerStat assister : assistProviders) {
                        if (!scorer.getPlayerId().equals(assister.getPlayerId())) {
                            // Apply synergy bonus
                            scorer.setSameTeamGoalAssistBonus(scorer.getSameTeamGoalAssistBonus() + 3);
                            assister.setSameTeamGoalAssistBonus(assister.getSameTeamGoalAssistBonus() + 3);
                            
                            log.info("Applied same-team goal-assist bonus for players {} and {}", 
                                    scorer.getPlayerId(), assister.getPlayerId());
                        }
                    }
                }
            }
        }
    }

    private void applySameTeamCleanSheetBonus(List<PlayerStat> fixtureStats) {
        // Group by team
        Map<Long, List<PlayerStat>> statsByTeam = fixtureStats.stream()
                .collect(Collectors.groupingBy(PlayerStat::getTeamId));

        for (List<PlayerStat> teamStats : statsByTeam.values()) {
            List<PlayerStat> cleanSheetPlayers = teamStats.stream()
                    .filter(stat -> stat.getCleanSheet())
                    .collect(Collectors.toList());

            if (cleanSheetPlayers.size() >= 2) {
                // Find goalkeeper and defenders with clean sheet
                boolean hasGoalkeeper = false;
                boolean hasDefender = false;

                for (PlayerStat stat : cleanSheetPlayers) {
                    Player player = playerRepository.findById(stat.getPlayerId()).orElse(null);
                    if (player != null) {
                        if (player.getPosition() == Player.Position.GOALKEEPER) {
                            hasGoalkeeper = true;
                        } else if (player.getPosition() == Player.Position.DEFENDER) {
                            hasDefender = true;
                        }
                    }
                }

                // Apply bonus if both goalkeeper and defender have clean sheet
                if (hasGoalkeeper && hasDefender) {
                    for (PlayerStat stat : cleanSheetPlayers) {
                        Player player = playerRepository.findById(stat.getPlayerId()).orElse(null);
                        if (player != null && isDefensivePosition(player.getPosition())) {
                            stat.setSameTeamCleanSheetBonus(stat.getSameTeamCleanSheetBonus() + 2);
                            log.info("Applied same-team clean sheet bonus for player {}", stat.getPlayerId());
                        }
                    }
                }
            }
        }
    }

    private void updateFantasyTeamPoints(Long gameWeekId) {
        List<FantasyTeam> allTeams = fantasyTeamRepository.findByIsActiveTrue();

        for (FantasyTeam team : allTeams) {
            int gameWeekPoints = 0;

            for (FantasyPlayer fantasyPlayer : team.getFantasyPlayers()) {
                PlayerStat stat = mockPlayerStatService
                        .findByPlayerIdAndFixtureId(fantasyPlayer.getPlayer().getId(), gameWeekId)
                        .orElse(null);

                if (stat != null) {
                    int playerPoints = stat.getFantasyPoints() + stat.getBonusPoints() + 
                                    stat.getSameTeamGoalAssistBonus() + stat.getSameTeamCleanSheetBonus();

                    // Apply captain multiplier
                    if (fantasyPlayer.getIsCaptain()) {
                        playerPoints *= 2;
                    } else if (fantasyPlayer.getIsViceCaptain() && !hasCaptainPlayed(team, gameWeekId)) {
                        playerPoints *= 2;
                    }

                    // Only count points for starting players
                    if (fantasyPlayer.getIsStarter()) {
                        gameWeekPoints += playerPoints;
                    }

                    fantasyPlayer.setGameweekPoints(playerPoints);
                    fantasyPlayer.addPoints(playerPoints);
                }
            }

            team.setGameweekPoints(gameWeekPoints);
            team.setTotalPoints(team.getTotalPoints() + gameWeekPoints);
            fantasyTeamRepository.save(team);

            log.info("Updated team {} points: {} (gameweek: {}, total: {})", 
                    team.getName(), gameWeekPoints, gameWeekPoints, team.getTotalPoints());
        }
    }

    private boolean hasCaptainPlayed(FantasyTeam team, Long gameWeekId) {
        return team.getFantasyPlayers().stream()
                .filter(FantasyPlayer::getIsCaptain)
                .anyMatch(fp -> {
                    PlayerStat stat = mockPlayerStatService
                            .findByPlayerIdAndFixtureId(fp.getPlayer().getId(), gameWeekId)
                            .orElse(null);
                    return stat != null && stat.getMinutesPlayed() > 0;
                });
    }

    private int getGoalPoints(Player.Position position) {
        return switch (position) {
            case GOALKEEPER -> 6;
            case DEFENDER -> 6;
            case MIDFIELDER -> 5;
            case FORWARD -> 4;
        };
    }

    private boolean isDefensivePosition(Player.Position position) {
        return position == Player.Position.GOALKEEPER || position == Player.Position.DEFENDER;
    }
}
