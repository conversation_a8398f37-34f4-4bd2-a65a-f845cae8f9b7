package Server.Fantasy_Server.service;

import Server.Fantasy_Server.document.AIRecommendation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
@ConditionalOnMissingBean(name = "aiRecommendationRepository")
@Slf4j
public class MockAIRecommendationService {

    private final List<AIRecommendation> mockRecommendations = new ArrayList<>();

    public MockAIRecommendationService() {
        initializeMockData();
    }

    private void initializeMockData() {
        // Create sample AI recommendations
        for (int i = 1; i <= 5; i++) {
            AIRecommendation recommendation = AIRecommendation.builder()
                    .id("mock_rec_" + i)
                    .playerId((long) i)
                    .gameWeekId(1L)
                    .season("2024")
                    .recommendationType(AIRecommendation.RecommendationType.PLAYER_OF_THE_WEEK)
                    .confidenceScore(new BigDecimal("0.8"))
                    .predictedPoints(new BigDecimal("10.5"))
                    .formRating(new BigDecimal("4.2"))
                    .recommendationReason("Mock recommendation for testing")
                    .algorithmVersion("1.0")
                    .createdAt(LocalDateTime.now())
                    .expiresAt(LocalDateTime.now().plusDays(7))
                    .isActive(true)
                    .build();
            mockRecommendations.add(recommendation);
        }
        log.info("Initialized {} mock AI recommendations", mockRecommendations.size());
    }

    public List<AIRecommendation> findActiveRecommendationsByGameWeek(Long gameWeekId) {
        return mockRecommendations.stream()
                .filter(rec -> rec.getGameWeekId().equals(gameWeekId) && rec.getIsActive())
                .collect(Collectors.toList());
    }

    public List<AIRecommendation> findActiveRecommendationsByTypeAndGameWeek(
            AIRecommendation.RecommendationType type, Long gameWeekId) {
        return mockRecommendations.stream()
                .filter(rec -> rec.getRecommendationType().equals(type) && 
                              rec.getGameWeekId().equals(gameWeekId) && 
                              rec.getIsActive())
                .collect(Collectors.toList());
    }

    public AIRecommendation save(AIRecommendation recommendation) {
        if (recommendation.getId() == null) {
            recommendation.setId("mock_rec_" + System.currentTimeMillis());
        }
        mockRecommendations.removeIf(rec -> rec.getId().equals(recommendation.getId()));
        mockRecommendations.add(recommendation);
        log.debug("Saved mock AI recommendation: {}", recommendation.getId());
        return recommendation;
    }

    public List<AIRecommendation> saveAll(List<AIRecommendation> recommendations) {
        recommendations.forEach(this::save);
        return recommendations;
    }

    public void generateDailyRecommendations() {
        log.info("Mock: Generated daily AI recommendations");
    }

    public void cleanupExpiredRecommendations() {
        int before = mockRecommendations.size();
        mockRecommendations.removeIf(rec -> rec.getExpiresAt().isBefore(LocalDateTime.now()));
        int after = mockRecommendations.size();
        log.info("Mock: Cleaned up {} expired recommendations", before - after);
    }

    public List<AIRecommendation> getRecommendationsForGameWeek(Long gameWeekId) {
        return findActiveRecommendationsByGameWeek(gameWeekId);
    }

    public List<AIRecommendation> getRecommendationsByType(AIRecommendation.RecommendationType type, Long gameWeekId) {
        return findActiveRecommendationsByTypeAndGameWeek(type, gameWeekId);
    }
}
