package Server.Fantasy_Server.repository;

import Server.Fantasy_Server.entity.FantasyPlayer;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface FantasyPlayerRepository extends JpaRepository<FantasyPlayer, Long> {

    List<FantasyPlayer> findByFantasyTeamId(Long fantasyTeamId);

    List<FantasyPlayer> findByPlayerId(Long playerId);

    Optional<FantasyPlayer> findByFantasyTeamIdAndPlayerId(Long fantasyTeamId, Long playerId);

    List<FantasyPlayer> findByIsCaptainTrue();

    List<FantasyPlayer> findByIsViceCaptainTrue();

    List<FantasyPlayer> findByIsStarterTrue();

    @Query("SELECT fp FROM FantasyPlayer fp WHERE fp.fantasyTeam.id = :teamId AND fp.isCaptain = true")
    Optional<FantasyPlayer> findCaptainByTeamId(@Param("teamId") Long teamId);

    @Query("SELECT fp FROM FantasyPlayer fp WHERE fp.fantasyTeam.id = :teamId AND fp.isViceCaptain = true")
    Optional<FantasyPlayer> findViceCaptainByTeamId(@Param("teamId") Long teamId);

    @Query("SELECT fp FROM FantasyPlayer fp WHERE fp.fantasyTeam.id = :teamId AND fp.isStarter = true")
    List<FantasyPlayer> findStartersByTeamId(@Param("teamId") Long teamId);

    @Query("SELECT COUNT(fp) FROM FantasyPlayer fp WHERE fp.player.id = :playerId")
    long countByPlayerId(@Param("playerId") Long playerId);

    @Query("SELECT fp FROM FantasyPlayer fp WHERE fp.fantasyTeam.user.id = :userId")
    List<FantasyPlayer> findByUserId(@Param("userId") Long userId);

    @Query("SELECT fp FROM FantasyPlayer fp WHERE fp.totalPoints >= :minPoints ORDER BY fp.totalPoints DESC")
    List<FantasyPlayer> findTopPerformers(@Param("minPoints") Integer minPoints);
}
