package Server.Fantasy_Server.repository;

import Server.Fantasy_Server.document.PlayerStat;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface PlayerStatRepository extends MongoRepository<PlayerStat, String> {

    List<PlayerStat> findByPlayerId(Long playerId);

    List<PlayerStat> findByFixtureId(Long fixtureId);

    List<PlayerStat> findByGameWeekId(Long gameWeekId);

    List<PlayerStat> findByTeamId(Long teamId);

    List<PlayerStat> findBySeason(String season);

    Optional<PlayerStat> findByPlayerIdAndFixtureId(Long playerId, Long fixtureId);

    List<PlayerStat> findByPlayerIdAndSeason(Long playerId, String season);

    @Query("{ 'playerId': ?0, 'gameWeekId': { $in: ?1 } }")
    List<PlayerStat> findByPlayerIdAndGameWeekIds(Long playerId, List<Long> gameWeekIds);

    @Query("{ 'teamId': ?0, 'gameWeekId': ?1 }")
    List<PlayerStat> findByTeamIdAndGameWeekId(Long teamId, Long gameWeekId);

    @Query("{ 'goals': { $gt: 0 }, 'gameWeekId': ?0 }")
    List<PlayerStat> findGoalScorersByGameWeek(Long gameWeekId);

    @Query("{ 'assists': { $gt: 0 }, 'gameWeekId': ?0 }")
    List<PlayerStat> findAssistProvidersByGameWeek(Long gameWeekId);

    @Query("{ 'cleanSheet': true, 'gameWeekId': ?0 }")
    List<PlayerStat> findCleanSheetPlayersByGameWeek(Long gameWeekId);

    @Query("{ 'fantasyPoints': { $gte: ?0 }, 'gameWeekId': ?1 }")
    List<PlayerStat> findTopPerformersByGameWeek(Integer minPoints, Long gameWeekId);

    @Query("{ 'playerId': ?0, 'season': ?1 }")
    List<PlayerStat> findPlayerSeasonStats(Long playerId, String season);

    @Query("{ 'teamId': ?0, 'season': ?1 }")
    List<PlayerStat> findTeamSeasonStats(Long teamId, String season);

    @Query(value = "{ 'gameWeekId': ?0 }", sort = "{ 'fantasyPoints': -1 }")
    List<PlayerStat> findTopPerformersInGameWeek(Long gameWeekId);

    @Query("{ 'playerId': ?0 }")
    List<PlayerStat> findAllPlayerStats(Long playerId);

    @Query("{ 'sameTeamGoalAssistBonus': { $gt: 0 } }")
    List<PlayerStat> findPlayersWithSameTeamBonus();

    @Query("{ 'sameTeamCleanSheetBonus': { $gt: 0 } }")
    List<PlayerStat> findPlayersWithCleanSheetBonus();
}
