package Server.Fantasy_Server.repository;

import Server.Fantasy_Server.entity.GameWeek;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface GameWeekRepository extends JpaRepository<GameWeek, Long> {

    Optional<GameWeek> findByIsCurrentTrue();

    List<GameWeek> findBySeason(String season);

    Optional<GameWeek> findBySeasonAndWeekNumber(String season, Integer weekNumber);

    List<GameWeek> findByIsFinishedFalse();

    List<GameWeek> findByIsFinishedTrue();

    @Query("SELECT gw FROM GameWeek gw WHERE gw.startDate <= :date AND gw.endDate >= :date")
    Optional<GameWeek> findByDate(@Param("date") LocalDateTime date);

    @Query("SELECT gw FROM GameWeek gw WHERE gw.deadline < :now AND gw.isFinished = false")
    List<GameWeek> findExpiredGameWeeks(@Param("now") LocalDateTime now);

    @Query("SELECT gw FROM GameWeek gw WHERE gw.season = :season ORDER BY gw.weekNumber")
    List<GameWeek> findBySeasonOrderByWeekNumber(@Param("season") String season);

    @Query("SELECT gw FROM GameWeek gw WHERE gw.deadline > :now ORDER BY gw.deadline ASC")
    List<GameWeek> findUpcomingGameWeeks(@Param("now") LocalDateTime now);

    @Query("SELECT MAX(gw.weekNumber) FROM GameWeek gw WHERE gw.season = :season")
    Optional<Integer> findMaxWeekNumberBySeason(@Param("season") String season);

    @Query("SELECT COUNT(gw) FROM GameWeek gw WHERE gw.season = :season AND gw.isFinished = true")
    long countFinishedGameWeeksBySeason(@Param("season") String season);
}
