package Server.Fantasy_Server.repository;

import Server.Fantasy_Server.entity.Player;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

@Repository
public interface PlayerRepository extends JpaRepository<Player, Long> {

    Optional<Player> findByExternalApiId(Long externalApiId);

    List<Player> findByTeamId(Long teamId);

    List<Player> findByPosition(Player.Position position);

    List<Player> findByIsActiveTrue();

    List<Player> findByIsInjuredTrue();

    @Query("SELECT p FROM Player p WHERE p.team.id = :teamId AND p.position = :position AND p.isActive = true")
    List<Player> findActivePlayersByTeamAndPosition(@Param("teamId") Long teamId, 
                                                   @Param("position") Player.Position position);

    @Query("SELECT p FROM Player p WHERE p.fantasyPrice BETWEEN :minPrice AND :maxPrice AND p.isActive = true")
    List<Player> findByPriceRange(@Param("minPrice") BigDecimal minPrice, 
                                 @Param("maxPrice") BigDecimal maxPrice);

    @Query("SELECT p FROM Player p WHERE p.totalPoints >= :minPoints AND p.isActive = true ORDER BY p.totalPoints DESC")
    List<Player> findTopPerformers(@Param("minPoints") Integer minPoints);

    @Query("SELECT p FROM Player p WHERE p.name LIKE %:name% OR p.firstName LIKE %:name% OR p.lastName LIKE %:name%")
    List<Player> findByNameContaining(@Param("name") String name);

    @Query("SELECT p FROM Player p WHERE p.formRating >= :minRating AND p.isActive = true ORDER BY p.formRating DESC")
    List<Player> findByFormRating(@Param("minRating") BigDecimal minRating);

    @Query("SELECT p FROM Player p JOIN p.fantasyPlayers fp GROUP BY p HAVING COUNT(fp) >= :minOwnership")
    List<Player> findPopularPlayers(@Param("minOwnership") Long minOwnership);

    boolean existsByExternalApiId(Long externalApiId);

    @Query("SELECT COUNT(p) FROM Player p WHERE p.team.id = :teamId AND p.position = :position AND p.isActive = true")
    long countByTeamAndPosition(@Param("teamId") Long teamId, @Param("position") Player.Position position);
}
