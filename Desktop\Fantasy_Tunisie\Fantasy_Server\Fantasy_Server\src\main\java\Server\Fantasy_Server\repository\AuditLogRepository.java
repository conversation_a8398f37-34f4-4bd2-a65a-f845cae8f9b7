package Server.Fantasy_Server.repository;

import Server.Fantasy_Server.document.AuditLog;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface AuditLogRepository extends MongoRepository<AuditLog, String> {

    List<AuditLog> findByUserId(Long userId);

    List<AuditLog> findByUsername(String username);

    List<AuditLog> findByAction(String action);

    List<AuditLog> findByEntityType(String entityType);

    List<AuditLog> findByEntityId(String entityId);

    @Query("{ 'timestamp': { $gte: ?0, $lte: ?1 } }")
    List<AuditLog> findByTimestampBetween(LocalDateTime start, LocalDateTime end);

    @Query("{ 'userId': ?0, 'timestamp': { $gte: ?1, $lte: ?2 } }")
    List<AuditLog> findByUserIdAndTimestampBetween(Long userId, LocalDateTime start, LocalDateTime end);

    @Query("{ 'action': ?0, 'timestamp': { $gte: ?1, $lte: ?2 } }")
    List<AuditLog> findByActionAndTimestampBetween(String action, LocalDateTime start, LocalDateTime end);

    @Query("{ 'entityType': ?0, 'entityId': ?1 }")
    List<AuditLog> findByEntityTypeAndEntityId(String entityType, String entityId);

    @Query("{ 'statusCode': { $gte: 400 } }")
    List<AuditLog> findErrorLogs();

    @Query("{ 'statusCode': { $gte: 500 } }")
    List<AuditLog> findServerErrorLogs();

    @Query("{ 'executionTimeMs': { $gte: ?0 } }")
    List<AuditLog> findSlowRequests(Long minExecutionTime);

    @Query("{ 'ipAddress': ?0 }")
    List<AuditLog> findByIpAddress(String ipAddress);

    @Query("{ 'sessionId': ?0 }")
    List<AuditLog> findBySessionId(String sessionId);

    @Query(value = "{ 'timestamp': { $gte: ?0 } }", sort = "{ 'timestamp': -1 }")
    List<AuditLog> findRecentLogs(LocalDateTime since);

    @Query("{ 'endpoint': ?0, 'timestamp': { $gte: ?1, $lte: ?2 } }")
    List<AuditLog> findByEndpointAndTimestampBetween(String endpoint, LocalDateTime start, LocalDateTime end);

    @Query("{ 'userId': ?0, 'action': { $in: ['LOGIN', 'LOGOUT', 'REGISTER'] } }")
    List<AuditLog> findUserAuthenticationLogs(Long userId);
}
