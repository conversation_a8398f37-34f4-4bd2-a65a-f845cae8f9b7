package Server.Fantasy_Server.service;

import Server.Fantasy_Server.document.PlayerStat;
import Server.Fantasy_Server.entity.*;
import Server.Fantasy_Server.repository.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Service
@RequiredArgsConstructor
@Slf4j
public class ExternalApiService {

    private final RestTemplate restTemplate;
    private final TeamRepository teamRepository;
    private final PlayerRepository playerRepository;
    private final FixtureRepository fixtureRepository;
    private final GameWeekRepository gameWeekRepository;
    private final PlayerStatRepository playerStatRepository;

    @Value("${external-api.football.base-url}")
    private String baseUrl;

    @Value("${external-api.football.api-key}")
    private String apiKey;

    @Value("${external-api.football.host}")
    private String apiHost;

    @Value("${external-api.football.league-id}")
    private String leagueId;

    @Value("${external-api.football.season}")
    private String season;

    @Transactional
    public void syncTeams() {
        log.info("Starting team synchronization for league: {}, season: {}", leagueId, season);

        try {
            String url = baseUrl + "/teams?league=" + leagueId + "&season=" + season;
            HttpHeaders headers = createHeaders();
            HttpEntity<String> entity = new HttpEntity<>(headers);

            ResponseEntity<Map> response = restTemplate.exchange(url, HttpMethod.GET, entity, Map.class);
            
            if (response.getBody() != null) {
                Map<String, Object> responseBody = response.getBody();
                List<Map<String, Object>> teams = (List<Map<String, Object>>) responseBody.get("response");

                for (Map<String, Object> teamData : teams) {
                    Map<String, Object> team = (Map<String, Object>) teamData.get("team");
                    syncTeam(team);
                }
            }

            log.info("Team synchronization completed successfully");
        } catch (Exception e) {
            log.error("Error during team synchronization: ", e);
            throw new RuntimeException("Failed to sync teams: " + e.getMessage());
        }
    }

    @Transactional
    public void syncPlayers() {
        log.info("Starting player synchronization for league: {}, season: {}", leagueId, season);

        try {
            List<Team> teams = teamRepository.findByIsActiveTrue();

            for (Team team : teams) {
                if (team.getExternalApiId() != null) {
                    syncTeamPlayers(team);
                }
            }

            log.info("Player synchronization completed successfully");
        } catch (Exception e) {
            log.error("Error during player synchronization: ", e);
            throw new RuntimeException("Failed to sync players: " + e.getMessage());
        }
    }

    @Transactional
    public void syncFixtures() {
        log.info("Starting fixture synchronization for league: {}, season: {}", leagueId, season);

        try {
            String url = baseUrl + "/fixtures?league=" + leagueId + "&season=" + season;
            HttpHeaders headers = createHeaders();
            HttpEntity<String> entity = new HttpEntity<>(headers);

            ResponseEntity<Map> response = restTemplate.exchange(url, HttpMethod.GET, entity, Map.class);
            
            if (response.getBody() != null) {
                Map<String, Object> responseBody = response.getBody();
                List<Map<String, Object>> fixtures = (List<Map<String, Object>>) responseBody.get("response");

                for (Map<String, Object> fixtureData : fixtures) {
                    syncFixture(fixtureData);
                }
            }

            log.info("Fixture synchronization completed successfully");
        } catch (Exception e) {
            log.error("Error during fixture synchronization: ", e);
            throw new RuntimeException("Failed to sync fixtures: " + e.getMessage());
        }
    }

    @Transactional
    public void syncPlayerStats(Long fixtureId) {
        log.info("Starting player stats synchronization for fixture: {}", fixtureId);

        try {
            Fixture fixture = fixtureRepository.findById(fixtureId)
                    .orElseThrow(() -> new RuntimeException("Fixture not found: " + fixtureId));

            if (fixture.getExternalApiId() == null) {
                log.warn("Fixture {} has no external API ID", fixtureId);
                return;
            }

            String url = baseUrl + "/fixtures/players?fixture=" + fixture.getExternalApiId();
            HttpHeaders headers = createHeaders();
            HttpEntity<String> entity = new HttpEntity<>(headers);

            ResponseEntity<Map> response = restTemplate.exchange(url, HttpMethod.GET, entity, Map.class);
            
            if (response.getBody() != null) {
                Map<String, Object> responseBody = response.getBody();
                List<Map<String, Object>> teams = (List<Map<String, Object>>) responseBody.get("response");

                for (Map<String, Object> teamData : teams) {
                    List<Map<String, Object>> players = (List<Map<String, Object>>) teamData.get("players");
                    
                    for (Map<String, Object> playerData : players) {
                        syncPlayerStat(playerData, fixture);
                    }
                }
            }

            log.info("Player stats synchronization completed for fixture: {}", fixtureId);
        } catch (Exception e) {
            log.error("Error during player stats synchronization for fixture {}: ", fixtureId, e);
            throw new RuntimeException("Failed to sync player stats: " + e.getMessage());
        }
    }

    private void syncTeam(Map<String, Object> teamData) {
        Long externalId = Long.valueOf(teamData.get("id").toString());
        String name = (String) teamData.get("name");
        String code = (String) teamData.get("code");
        String logo = (String) teamData.get("logo");
        Integer founded = (Integer) teamData.get("founded");

        Optional<Team> existingTeam = teamRepository.findByExternalApiId(externalId);

        Team team;
        if (existingTeam.isPresent()) {
            team = existingTeam.get();
        } else {
            team = new Team();
            team.setExternalApiId(externalId);
        }

        team.setName(name);
        team.setShortName(code != null ? code : name.substring(0, Math.min(3, name.length())).toUpperCase());
        team.setLogoUrl(logo);
        team.setFoundedYear(founded);
        team.setIsActive(true);

        teamRepository.save(team);
        log.debug("Synced team: {}", name);
    }

    private void syncTeamPlayers(Team team) {
        try {
            String url = baseUrl + "/players?team=" + team.getExternalApiId() + "&season=" + season;
            HttpHeaders headers = createHeaders();
            HttpEntity<String> entity = new HttpEntity<>(headers);

            ResponseEntity<Map> response = restTemplate.exchange(url, HttpMethod.GET, entity, Map.class);
            
            if (response.getBody() != null) {
                Map<String, Object> responseBody = response.getBody();
                List<Map<String, Object>> players = (List<Map<String, Object>>) responseBody.get("response");

                for (Map<String, Object> playerData : players) {
                    syncPlayer(playerData, team);
                }
            }
        } catch (Exception e) {
            log.error("Error syncing players for team {}: ", team.getName(), e);
        }
    }

    private void syncPlayer(Map<String, Object> playerData, Team team) {
        Map<String, Object> player = (Map<String, Object>) playerData.get("player");
        List<Map<String, Object>> statistics = (List<Map<String, Object>>) playerData.get("statistics");

        if (statistics.isEmpty()) return;

        Map<String, Object> stats = statistics.get(0); // Take first statistics entry
        Map<String, Object> games = (Map<String, Object>) stats.get("games");

        Long externalId = Long.valueOf(player.get("id").toString());
        String name = (String) player.get("name");
        String firstName = (String) player.get("firstname");
        String lastName = (String) player.get("lastname");
        String position = (String) games.get("position");

        Optional<Player> existingPlayer = playerRepository.findByExternalApiId(externalId);

        Player playerEntity;
        if (existingPlayer.isPresent()) {
            playerEntity = existingPlayer.get();
        } else {
            playerEntity = new Player();
            playerEntity.setExternalApiId(externalId);
        }

        playerEntity.setName(name);
        playerEntity.setFirstName(firstName);
        playerEntity.setLastName(lastName);
        playerEntity.setTeam(team);
        playerEntity.setPosition(mapPosition(position));
        playerEntity.setIsActive(true);
        
        // Set default fantasy price based on position
        playerEntity.setFantasyPrice(getDefaultPrice(mapPosition(position)));

        playerRepository.save(playerEntity);
        log.debug("Synced player: {} for team: {}", name, team.getName());
    }

    private void syncFixture(Map<String, Object> fixtureData) {
        Map<String, Object> fixture = (Map<String, Object>) fixtureData.get("fixture");
        Map<String, Object> teams = (Map<String, Object>) fixtureData.get("teams");
        Map<String, Object> goals = (Map<String, Object>) fixtureData.get("goals");

        Long externalId = Long.valueOf(fixture.get("id").toString());
        String dateStr = (String) fixture.get("date");
        String status = (String) ((Map<String, Object>) fixture.get("status")).get("short");

        Map<String, Object> homeTeam = (Map<String, Object>) teams.get("home");
        Map<String, Object> awayTeam = (Map<String, Object>) teams.get("away");

        Long homeTeamId = Long.valueOf(homeTeam.get("id").toString());
        Long awayTeamId = Long.valueOf(awayTeam.get("id").toString());

        Optional<Team> home = teamRepository.findByExternalApiId(homeTeamId);
        Optional<Team> away = teamRepository.findByExternalApiId(awayTeamId);

        if (home.isEmpty() || away.isEmpty()) {
            log.warn("Teams not found for fixture {}: home={}, away={}", externalId, homeTeamId, awayTeamId);
            return;
        }

        Optional<Fixture> existingFixture = fixtureRepository.findByExternalApiId(externalId);

        Fixture fixtureEntity;
        if (existingFixture.isPresent()) {
            fixtureEntity = existingFixture.get();
        } else {
            fixtureEntity = new Fixture();
            fixtureEntity.setExternalApiId(externalId);
        }

        fixtureEntity.setHomeTeam(home.get());
        fixtureEntity.setAwayTeam(away.get());
        fixtureEntity.setKickOffTime(LocalDateTime.parse(dateStr, DateTimeFormatter.ISO_DATE_TIME));
        fixtureEntity.setStatus(mapFixtureStatus(status));

        if (goals.get("home") != null) {
            fixtureEntity.setHomeScore((Integer) goals.get("home"));
        }
        if (goals.get("away") != null) {
            fixtureEntity.setAwayScore((Integer) goals.get("away"));
        }

        fixtureRepository.save(fixtureEntity);
        log.debug("Synced fixture: {} vs {}", home.get().getName(), away.get().getName());
    }

    private void syncPlayerStat(Map<String, Object> playerData, Fixture fixture) {
        Map<String, Object> player = (Map<String, Object>) playerData.get("player");
        List<Map<String, Object>> statistics = (List<Map<String, Object>>) playerData.get("statistics");

        if (statistics.isEmpty()) return;

        Long externalPlayerId = Long.valueOf(player.get("id").toString());
        Optional<Player> playerEntity = playerRepository.findByExternalApiId(externalPlayerId);

        if (playerEntity.isEmpty()) {
            log.warn("Player not found for external ID: {}", externalPlayerId);
            return;
        }

        Map<String, Object> stats = statistics.get(0);
        Map<String, Object> games = (Map<String, Object>) stats.get("games");
        Map<String, Object> goals = (Map<String, Object>) stats.get("goals");
        Map<String, Object> passes = (Map<String, Object>) stats.get("passes");
        Map<String, Object> tackles = (Map<String, Object>) stats.get("tackles");
        Map<String, Object> cards = (Map<String, Object>) stats.get("cards");

        PlayerStat playerStat = PlayerStat.builder()
                .playerId(playerEntity.get().getId())
                .fixtureId(fixture.getId())
                .teamId(playerEntity.get().getTeam().getId())
                .gameWeekId(fixture.getGameWeek() != null ? fixture.getGameWeek().getId() : null)
                .season(season)
                .minutesPlayed(getIntValue(games, "minutes"))
                .goals(getIntValue(goals, "total"))
                .assists(getIntValue(goals, "assists"))
                .yellowCards(getIntValue(cards, "yellow"))
                .redCards(getIntValue(cards, "red"))
                .passes(getIntValue(passes, "total"))
                .passesCompleted(getIntValue(passes, "accuracy"))
                .tackles(getIntValue(tackles, "total"))
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .build();

        // Calculate clean sheet
        if (isDefensivePosition(playerEntity.get().getPosition())) {
            boolean isHome = playerEntity.get().getTeam().getId().equals(fixture.getHomeTeam().getId());
            Integer goalsConceded = isHome ? fixture.getAwayScore() : fixture.getHomeScore();
            playerStat.setCleanSheet(goalsConceded != null && goalsConceded == 0);
        }

        playerStat.calculateFantasyPoints();
        playerStatRepository.save(playerStat);

        log.debug("Synced player stat for: {} in fixture: {}", playerEntity.get().getName(), fixture.getId());
    }

    private HttpHeaders createHeaders() {
        HttpHeaders headers = new HttpHeaders();
        headers.set("X-RapidAPI-Key", apiKey);
        headers.set("X-RapidAPI-Host", apiHost);
        return headers;
    }

    private Player.Position mapPosition(String position) {
        if (position == null) return Player.Position.MIDFIELDER;
        
        return switch (position.toUpperCase()) {
            case "G", "GOALKEEPER" -> Player.Position.GOALKEEPER;
            case "D", "DEFENDER" -> Player.Position.DEFENDER;
            case "M", "MIDFIELDER" -> Player.Position.MIDFIELDER;
            case "F", "FORWARD", "ATTACKER" -> Player.Position.FORWARD;
            default -> Player.Position.MIDFIELDER;
        };
    }

    private Fixture.FixtureStatus mapFixtureStatus(String status) {
        return switch (status) {
            case "NS" -> Fixture.FixtureStatus.SCHEDULED;
            case "1H", "2H", "ET", "P" -> Fixture.FixtureStatus.LIVE;
            case "HT" -> Fixture.FixtureStatus.HALFTIME;
            case "FT", "AET", "PEN" -> Fixture.FixtureStatus.FINISHED;
            case "PST" -> Fixture.FixtureStatus.POSTPONED;
            case "CANC" -> Fixture.FixtureStatus.CANCELLED;
            case "SUSP" -> Fixture.FixtureStatus.SUSPENDED;
            default -> Fixture.FixtureStatus.SCHEDULED;
        };
    }

    private BigDecimal getDefaultPrice(Player.Position position) {
        return switch (position) {
            case GOALKEEPER -> new BigDecimal("4500000");
            case DEFENDER -> new BigDecimal("5000000");
            case MIDFIELDER -> new BigDecimal("6000000");
            case FORWARD -> new BigDecimal("7000000");
        };
    }

    private boolean isDefensivePosition(Player.Position position) {
        return position == Player.Position.GOALKEEPER || position == Player.Position.DEFENDER;
    }

    private Integer getIntValue(Map<String, Object> map, String key) {
        if (map == null || map.get(key) == null) return 0;
        Object value = map.get(key);
        if (value instanceof Integer) return (Integer) value;
        if (value instanceof String) {
            try {
                return Integer.parseInt((String) value);
            } catch (NumberFormatException e) {
                return 0;
            }
        }
        return 0;
    }
}
