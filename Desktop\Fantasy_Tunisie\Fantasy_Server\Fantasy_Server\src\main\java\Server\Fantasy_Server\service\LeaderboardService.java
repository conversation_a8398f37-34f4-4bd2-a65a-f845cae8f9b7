package Server.Fantasy_Server.service;

import Server.Fantasy_Server.dto.LeaderboardDto;
import Server.Fantasy_Server.entity.FantasyTeam;
import Server.Fantasy_Server.entity.Leaderboard;
import Server.Fantasy_Server.repository.FantasyTeamRepository;
import Server.Fantasy_Server.repository.LeaderboardRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class LeaderboardService {

    private final LeaderboardRepository leaderboardRepository;
    private final FantasyTeamRepository fantasyTeamRepository;

    public Page<LeaderboardDto> getOverallLeaderboard(Pageable pageable) {
        Page<Leaderboard> leaderboards = leaderboardRepository.findByTypeOrderByOverallRankAsc(
                Leaderboard.LeaderboardType.OVERALL, pageable);
        
        List<LeaderboardDto> dtos = leaderboards.getContent().stream()
                .map(this::mapToLeaderboardDto)
                .collect(Collectors.toList());
        
        return new PageImpl<>(dtos, pageable, leaderboards.getTotalElements());
    }

    public Page<LeaderboardDto> getGameWeekLeaderboard(Long gameWeekId, Pageable pageable) {
        Page<Leaderboard> leaderboards = leaderboardRepository.findByGameWeekIdAndTypeOrderByGameweekRankAsc(
                gameWeekId, Leaderboard.LeaderboardType.GAMEWEEK, pageable);
        
        List<LeaderboardDto> dtos = leaderboards.getContent().stream()
                .map(this::mapToLeaderboardDto)
                .collect(Collectors.toList());
        
        return new PageImpl<>(dtos, pageable, leaderboards.getTotalElements());
    }

    public Page<LeaderboardDto> getMonthlyLeaderboard(Pageable pageable) {
        Page<Leaderboard> leaderboards = leaderboardRepository.findByTypeOrderByOverallRankAsc(
                Leaderboard.LeaderboardType.MONTHLY, pageable);
        
        List<LeaderboardDto> dtos = leaderboards.getContent().stream()
                .map(this::mapToLeaderboardDto)
                .collect(Collectors.toList());
        
        return new PageImpl<>(dtos, pageable, leaderboards.getTotalElements());
    }

    @Transactional
    public void updateAllLeaderboards() {
        log.info("Starting leaderboard update");
        
        updateOverallLeaderboard();
        // updateGameWeekLeaderboards(); // Would need current gameweek
        // updateMonthlyLeaderboards(); // Would need month calculation
        
        log.info("Leaderboard update completed");
    }

    @Transactional
    public void updateOverallLeaderboard() {
        log.info("Updating overall leaderboard");
        
        // Get all teams ordered by total points
        List<FantasyTeam> teams = fantasyTeamRepository.findAllOrderByTotalPointsDesc();
        
        // Clear existing overall leaderboard
        leaderboardRepository.deleteByType(Leaderboard.LeaderboardType.OVERALL);
        
        AtomicInteger rank = new AtomicInteger(1);
        
        for (FantasyTeam team : teams) {
            Leaderboard leaderboard = Leaderboard.builder()
                    .user(team.getUser())
                    .fantasyTeam(team)
                    .totalPoints(team.getTotalPoints())
                    .gameweekPoints(team.getGameweekPoints())
                    .overallRank(rank.getAndIncrement())
                    .type(Leaderboard.LeaderboardType.OVERALL)
                    .build();
            
            leaderboardRepository.save(leaderboard);
        }
        
        log.info("Overall leaderboard updated with {} teams", teams.size());
    }

    @Transactional
    public void updateGameWeekLeaderboard(Long gameWeekId) {
        log.info("Updating gameweek leaderboard for gameweek: {}", gameWeekId);
        
        // Get all teams ordered by gameweek points
        List<FantasyTeam> teams = fantasyTeamRepository.findAllOrderByGameweekPointsDesc();
        
        // Clear existing gameweek leaderboard
        leaderboardRepository.deleteByGameWeekIdAndType(gameWeekId, Leaderboard.LeaderboardType.GAMEWEEK);
        
        AtomicInteger rank = new AtomicInteger(1);
        
        for (FantasyTeam team : teams) {
            Leaderboard leaderboard = Leaderboard.builder()
                    .user(team.getUser())
                    .fantasyTeam(team)
                    .totalPoints(team.getTotalPoints())
                    .gameweekPoints(team.getGameweekPoints())
                    .gameweekRank(rank.getAndIncrement())
                    .type(Leaderboard.LeaderboardType.GAMEWEEK)
                    .build();
            
            leaderboardRepository.save(leaderboard);
        }
        
        log.info("Gameweek leaderboard updated with {} teams", teams.size());
    }

    private LeaderboardDto mapToLeaderboardDto(Leaderboard leaderboard) {
        return LeaderboardDto.builder()
                .id(leaderboard.getId())
                .username(leaderboard.getUser().getUsername())
                .fantasyTeamName(leaderboard.getFantasyTeam().getName())
                .totalPoints(leaderboard.getTotalPoints())
                .gameweekPoints(leaderboard.getGameweekPoints())
                .overallRank(leaderboard.getOverallRank())
                .gameweekRank(leaderboard.getGameweekRank())
                .previousRank(leaderboard.getPreviousRank())
                .rankChange(leaderboard.getRankChange())
                .type(leaderboard.getType())
                .build();
    }
}
