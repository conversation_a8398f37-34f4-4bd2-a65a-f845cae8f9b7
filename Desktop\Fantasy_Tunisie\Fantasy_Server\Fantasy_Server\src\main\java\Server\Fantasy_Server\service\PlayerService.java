package Server.Fantasy_Server.service;

import Server.Fantasy_Server.dto.PlayerDto;
import Server.Fantasy_Server.dto.TeamDto;
import Server.Fantasy_Server.entity.Player;
import Server.Fantasy_Server.repository.PlayerRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class PlayerService {

    private final PlayerRepository playerRepository;

    public Page<PlayerDto> getAllPlayers(Pageable pageable) {
        return playerRepository.findAll(pageable)
                .map(this::mapToPlayerDto);
    }

    public PlayerDto getPlayer(Long playerId) {
        Player player = playerRepository.findById(playerId)
                .orElseThrow(() -> new RuntimeException("Player not found"));
        return mapToPlayerDto(player);
    }

    public List<PlayerDto> getPlayersByPosition(Player.Position position) {
        return playerRepository.findByPosition(position).stream()
                .map(this::mapToPlayerDto)
                .collect(Collectors.toList());
    }

    public List<PlayerDto> getPlayersByTeam(Long teamId) {
        return playerRepository.findByTeamId(teamId).stream()
                .map(this::mapToPlayerDto)
                .collect(Collectors.toList());
    }

    public List<PlayerDto> searchPlayersByName(String name) {
        return playerRepository.findByNameContaining(name).stream()
                .map(this::mapToPlayerDto)
                .collect(Collectors.toList());
    }

    public List<PlayerDto> getPlayersByPriceRange(BigDecimal minPrice, BigDecimal maxPrice) {
        return playerRepository.findByPriceRange(minPrice, maxPrice).stream()
                .map(this::mapToPlayerDto)
                .collect(Collectors.toList());
    }

    public List<PlayerDto> getTopPerformers(Integer minPoints) {
        return playerRepository.findTopPerformers(minPoints).stream()
                .map(this::mapToPlayerDto)
                .collect(Collectors.toList());
    }

    public List<PlayerDto> getPopularPlayers(Long minOwnership) {
        return playerRepository.findPopularPlayers(minOwnership).stream()
                .map(this::mapToPlayerDto)
                .collect(Collectors.toList());
    }

    public List<PlayerDto> getInjuredPlayers() {
        return playerRepository.findByIsInjuredTrue().stream()
                .map(this::mapToPlayerDto)
                .collect(Collectors.toList());
    }

    private PlayerDto mapToPlayerDto(Player player) {
        return PlayerDto.builder()
                .id(player.getId())
                .name(player.getName())
                .firstName(player.getFirstName())
                .lastName(player.getLastName())
                .position(player.getPosition())
                .team(mapToTeamDto(player.getTeam()))
                .jerseyNumber(player.getJerseyNumber())
                .birthDate(player.getBirthDate())
                .nationality(player.getNationality())
                .heightCm(player.getHeightCm())
                .weightKg(player.getWeightKg())
                .photoUrl(player.getPhotoUrl())
                .marketValue(player.getMarketValue())
                .fantasyPrice(player.getFantasyPrice())
                .totalPoints(player.getTotalPoints())
                .formRating(player.getFormRating())
                .isActive(player.getIsActive())
                .isInjured(player.getIsInjured())
                .injuryDetails(player.getInjuryDetails())
                .build();
    }

    private TeamDto mapToTeamDto(Server.Fantasy_Server.entity.Team team) {
        return TeamDto.builder()
                .id(team.getId())
                .name(team.getName())
                .shortName(team.getShortName())
                .logoUrl(team.getLogoUrl())
                .foundedYear(team.getFoundedYear())
                .stadiumName(team.getStadiumName())
                .stadiumCapacity(team.getStadiumCapacity())
                .city(team.getCity())
                .isActive(team.getIsActive())
                .build();
    }
}
