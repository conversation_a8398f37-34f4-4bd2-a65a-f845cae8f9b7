package Server.Fantasy_Server.repository;

import Server.Fantasy_Server.entity.Leaderboard;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface LeaderboardRepository extends JpaRepository<Leaderboard, Long> {

    Page<Leaderboard> findByTypeOrderByOverallRankAsc(Leaderboard.LeaderboardType type, Pageable pageable);

    Page<Leaderboard> findByGameWeekIdAndTypeOrderByGameweekRankAsc(Long gameWeekId, Leaderboard.LeaderboardType type, Pageable pageable);

    List<Leaderboard> findByType(Leaderboard.LeaderboardType type);

    List<Leaderboard> findByGameWeekId(Long gameWeekId);

    Optional<Leaderboard> findByUserIdAndType(Long userId, Leaderboard.LeaderboardType type);

    Optional<Leaderboard> findByUserIdAndGameWeekIdAndType(Long userId, Long gameWeekId, Leaderboard.LeaderboardType type);

    @Query("SELECT l FROM Leaderboard l WHERE l.type = :type ORDER BY l.totalPoints DESC")
    List<Leaderboard> findByTypeOrderByTotalPointsDesc(@Param("type") Leaderboard.LeaderboardType type);

    @Query("SELECT l FROM Leaderboard l WHERE l.gameWeek.id = :gameWeekId AND l.type = :type ORDER BY l.gameweekPoints DESC")
    List<Leaderboard> findByGameWeekIdAndTypeOrderByGameweekPointsDesc(@Param("gameWeekId") Long gameWeekId, @Param("type") Leaderboard.LeaderboardType type);

    @Query("SELECT l FROM Leaderboard l WHERE l.overallRank <= :topN AND l.type = 'OVERALL' ORDER BY l.overallRank ASC")
    List<Leaderboard> findTopNOverall(@Param("topN") Integer topN);

    @Query("SELECT l FROM Leaderboard l WHERE l.gameweekRank <= :topN AND l.gameWeek.id = :gameWeekId AND l.type = 'GAMEWEEK' ORDER BY l.gameweekRank ASC")
    List<Leaderboard> findTopNGameweek(@Param("topN") Integer topN, @Param("gameWeekId") Long gameWeekId);

    @Modifying
    @Query("DELETE FROM Leaderboard l WHERE l.type = :type")
    void deleteByType(@Param("type") Leaderboard.LeaderboardType type);

    @Modifying
    @Query("DELETE FROM Leaderboard l WHERE l.gameWeek.id = :gameWeekId AND l.type = :type")
    void deleteByGameWeekIdAndType(@Param("gameWeekId") Long gameWeekId, @Param("type") Leaderboard.LeaderboardType type);

    @Query("SELECT COUNT(l) FROM Leaderboard l WHERE l.totalPoints > :points AND l.type = 'OVERALL'")
    long countUsersAbovePoints(@Param("points") Integer points);

    @Query("SELECT AVG(l.totalPoints) FROM Leaderboard l WHERE l.type = 'OVERALL'")
    Double getAveragePoints();
}
