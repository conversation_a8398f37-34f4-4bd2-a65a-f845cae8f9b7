package Server.Fantasy_Server.repository;

import Server.Fantasy_Server.entity.Team;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface TeamRepository extends JpaRepository<Team, Long> {

    Optional<Team> findByName(String name);

    Optional<Team> findByShortName(String shortName);

    Optional<Team> findByExternalApiId(Long externalApiId);

    List<Team> findByIsActiveTrue();

    List<Team> findByCity(String city);

    @Query("SELECT t FROM Team t WHERE t.name LIKE %:name% OR t.shortName LIKE %:name%")
    List<Team> findByNameContaining(@Param("name") String name);

    @Query("SELECT t FROM Team t JOIN t.players p WHERE p.id = :playerId")
    Optional<Team> findByPlayerId(@Param("playerId") Long playerId);

    @Query("SELECT COUNT(p) FROM Team t JOIN t.players p WHERE t.id = :teamId AND p.isActive = true")
    long countActivePlayersByTeamId(@Param("teamId") Long teamId);

    boolean existsByExternalApiId(Long externalApiId);
}
