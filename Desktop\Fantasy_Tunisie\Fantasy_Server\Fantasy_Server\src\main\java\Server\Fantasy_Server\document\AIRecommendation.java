package Server.Fantasy_Server.document;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@Document(collection = "ai_recommendations")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AIRecommendation {

    @Id
    private String id;

    @Field("player_id")
    private Long playerId;

    @Field("game_week_id")
    private Long gameWeekId;

    @Field("season")
    private String season;

    @Field("recommendation_type")
    private RecommendationType recommendationType;

    @Field("confidence_score")
    private BigDecimal confidenceScore; // 0.0 to 1.0

    @Field("predicted_points")
    private BigDecimal predictedPoints;

    @Field("form_rating")
    private BigDecimal formRating;

    @Field("difficulty_rating")
    private BigDecimal difficultyRating; // Opposition strength

    @Field("injury_risk")
    private BigDecimal injuryRisk;

    @Field("ownership_percentage")
    private BigDecimal ownershipPercentage;

    @Field("price_change_prediction")
    private BigDecimal priceChangePrediction;

    // Factors influencing the recommendation
    @Field("recent_form")
    private List<Integer> recentForm; // Last 5 gameweek points

    @Field("home_away_factor")
    private String homeAwayFactor; // HOME, AWAY

    @Field("opposition_strength")
    private BigDecimal oppositionStrength;

    @Field("historical_performance")
    private Map<String, Object> historicalPerformance;

    // Reasoning
    @Field("recommendation_reason")
    private String recommendationReason;

    @Field("key_factors")
    private List<String> keyFactors;

    @Field("risk_factors")
    private List<String> riskFactors;

    // Metadata
    @Field("algorithm_version")
    private String algorithmVersion;

    @Field("created_at")
    private LocalDateTime createdAt;

    @Field("expires_at")
    private LocalDateTime expiresAt;

    @Field("is_active")
    private Boolean isActive = true;

    public enum RecommendationType {
        PLAYER_OF_THE_WEEK,
        CAPTAIN_PICK,
        TRANSFER_IN,
        TRANSFER_OUT,
        DIFFERENTIAL_PICK,
        BUDGET_OPTION,
        PREMIUM_PICK,
        AVOID
    }

    public boolean isExpired() {
        return expiresAt != null && LocalDateTime.now().isAfter(expiresAt);
    }

    public boolean isHighConfidence() {
        return confidenceScore != null && confidenceScore.compareTo(new BigDecimal("0.8")) >= 0;
    }

    public boolean isMediumConfidence() {
        return confidenceScore != null && 
               confidenceScore.compareTo(new BigDecimal("0.5")) >= 0 && 
               confidenceScore.compareTo(new BigDecimal("0.8")) < 0;
    }

    public boolean isLowConfidence() {
        return confidenceScore != null && confidenceScore.compareTo(new BigDecimal("0.5")) < 0;
    }
}
