package Server.Fantasy_Server.service;

import Server.Fantasy_Server.document.AIRecommendation;

import java.util.List;

public interface AIRecommendationService {
    
    List<AIRecommendation> getRecommendationsForGameWeek(Long gameWeekId);
    
    List<AIRecommendation> getRecommendationsByType(AIRecommendation.RecommendationType type, Long gameWeekId);
    
    void generateDailyRecommendations();
    
    void cleanupExpiredRecommendations();
}
