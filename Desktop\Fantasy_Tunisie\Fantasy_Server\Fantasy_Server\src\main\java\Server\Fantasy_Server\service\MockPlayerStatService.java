package Server.Fantasy_Server.service;

import Server.Fantasy_Server.document.PlayerStat;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Service
@ConditionalOnMissingBean(name = "playerStatRepository")
@Slf4j
public class MockPlayerStatService implements PlayerStatService {

    private final List<PlayerStat> mockStats = new ArrayList<>();

    public MockPlayerStatService() {
        // Initialize with some mock data
        initializeMockData();
    }

    private void initializeMockData() {
        // Create some sample player stats
        for (int i = 1; i <= 10; i++) {
            PlayerStat stat = PlayerStat.builder()
                    .id("mock_" + i)
                    .playerId((long) i)
                    .fixtureId(1L)
                    .teamId(1L)
                    .gameWeekId(1L)
                    .season("2024")
                    .minutesPlayed(90)
                    .goals((int) (Math.random() * 3))
                    .assists((int) (Math.random() * 2))
                    .yellowCards(0)
                    .redCards(0)
                    .fantasyPoints((int) (Math.random() * 15))
                    .createdAt(LocalDateTime.now())
                    .updatedAt(LocalDateTime.now())
                    .build();
            mockStats.add(stat);
        }
        log.info("Initialized {} mock player stats", mockStats.size());
    }

    @Override
    public List<PlayerStat> findByPlayerId(Long playerId) {
        return mockStats.stream()
                .filter(stat -> stat.getPlayerId().equals(playerId))
                .toList();
    }

    @Override
    public List<PlayerStat> findByFixtureId(Long fixtureId) {
        return mockStats.stream()
                .filter(stat -> stat.getFixtureId().equals(fixtureId))
                .toList();
    }

    @Override
    public List<PlayerStat> findByGameWeekId(Long gameWeekId) {
        return mockStats.stream()
                .filter(stat -> stat.getGameWeekId().equals(gameWeekId))
                .toList();
    }

    @Override
    public List<PlayerStat> findByTeamId(Long teamId) {
        return mockStats.stream()
                .filter(stat -> stat.getTeamId().equals(teamId))
                .toList();
    }

    @Override
    public List<PlayerStat> findBySeason(String season) {
        return mockStats.stream()
                .filter(stat -> season.equals(stat.getSeason()))
                .toList();
    }

    @Override
    public Optional<PlayerStat> findByPlayerIdAndFixtureId(Long playerId, Long fixtureId) {
        return mockStats.stream()
                .filter(stat -> stat.getPlayerId().equals(playerId) && stat.getFixtureId().equals(fixtureId))
                .findFirst();
    }

    @Override
    public List<PlayerStat> findByPlayerIdAndSeason(Long playerId, String season) {
        return mockStats.stream()
                .filter(stat -> stat.getPlayerId().equals(playerId) && season.equals(stat.getSeason()))
                .toList();
    }

    @Override
    public PlayerStat save(PlayerStat playerStat) {
        if (playerStat.getId() == null) {
            playerStat.setId("mock_" + System.currentTimeMillis());
        }
        mockStats.removeIf(stat -> stat.getId().equals(playerStat.getId()));
        mockStats.add(playerStat);
        log.debug("Saved mock player stat: {}", playerStat.getId());
        return playerStat;
    }

    @Override
    public List<PlayerStat> saveAll(List<PlayerStat> playerStats) {
        playerStats.forEach(this::save);
        return playerStats;
    }

    public List<PlayerStat> findAll() {
        return new ArrayList<>(mockStats);
    }
}
