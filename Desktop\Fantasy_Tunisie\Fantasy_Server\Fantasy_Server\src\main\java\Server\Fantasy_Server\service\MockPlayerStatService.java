package Server.Fantasy_Server.service;

import Server.Fantasy_Server.document.PlayerStat;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Service
@ConditionalOnMissingBean(name = "playerStatRepository")
@Slf4j
public class MockPlayerStatService {

    private final List<PlayerStat> mockStats = new ArrayList<>();

    public MockPlayerStatService() {
        // Initialize with some mock data
        initializeMockData();
    }

    private void initializeMockData() {
        // Create some sample player stats
        for (int i = 1; i <= 10; i++) {
            PlayerStat stat = PlayerStat.builder()
                    .id("mock_" + i)
                    .playerId((long) i)
                    .fixtureId(1L)
                    .teamId(1L)
                    .gameWeekId(1L)
                    .season("2024")
                    .minutesPlayed(90)
                    .goals((int) (Math.random() * 3))
                    .assists((int) (Math.random() * 2))
                    .yellowCards(0)
                    .redCards(0)
                    .fantasyPoints((int) (Math.random() * 15))
                    .createdAt(LocalDateTime.now())
                    .updatedAt(LocalDateTime.now())
                    .build();
            mockStats.add(stat);
        }
        log.info("Initialized {} mock player stats", mockStats.size());
    }

    public List<PlayerStat> findByPlayerId(Long playerId) {
        return mockStats.stream()
                .filter(stat -> stat.getPlayerId().equals(playerId))
                .toList();
    }

    public List<PlayerStat> findByGameWeekId(Long gameWeekId) {
        return mockStats.stream()
                .filter(stat -> stat.getGameWeekId().equals(gameWeekId))
                .toList();
    }

    public Optional<PlayerStat> findByPlayerIdAndFixtureId(Long playerId, Long fixtureId) {
        return mockStats.stream()
                .filter(stat -> stat.getPlayerId().equals(playerId) && stat.getFixtureId().equals(fixtureId))
                .findFirst();
    }

    public PlayerStat save(PlayerStat playerStat) {
        if (playerStat.getId() == null) {
            playerStat.setId("mock_" + System.currentTimeMillis());
        }
        mockStats.removeIf(stat -> stat.getId().equals(playerStat.getId()));
        mockStats.add(playerStat);
        log.debug("Saved mock player stat: {}", playerStat.getId());
        return playerStat;
    }

    public List<PlayerStat> saveAll(List<PlayerStat> playerStats) {
        playerStats.forEach(this::save);
        return playerStats;
    }

    public List<PlayerStat> findAll() {
        return new ArrayList<>(mockStats);
    }
}
