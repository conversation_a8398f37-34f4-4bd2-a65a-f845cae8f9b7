spring:
  # PostgreSQL Configuration for Docker
  datasource:
    url: ************************************************
    username: ${DB_USERNAME:fantasy_user}
    password: ${DB_PASSWORD:fantasy_password}
    driver-class-name: org.postgresql.Driver
  
  # JPA Configuration for Docker
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: ${SHOW_SQL:false}
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
        jdbc:
          time_zone: UTC
  
  # MongoDB Configuration for Docker
  data:
    mongodb:
      uri: **************************************************************************************************************
      auto-index-creation: true

# Logging for Docker environment
logging:
  level:
    com.fantasy.tunisie: INFO
    org.springframework.data.mongodb: DEBUG
    org.springframework.security: WARN
