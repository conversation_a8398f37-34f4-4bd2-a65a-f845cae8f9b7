package Server.Fantasy_Server.service;

import Server.Fantasy_Server.dto.TransferRequest;
import Server.Fantasy_Server.entity.*;
import Server.Fantasy_Server.repository.*;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;

@Service
@RequiredArgsConstructor
public class TransferService {

    private final TransferRepository transferRepository;
    private final FantasyTeamRepository fantasyTeamRepository;
    private final FantasyPlayerRepository fantasyPlayerRepository;
    private final PlayerRepository playerRepository;
    private final UserRepository userRepository;
    private final GameWeekRepository gameWeekRepository;

    @Transactional
    public Transfer makeTransfer(TransferRequest request, String username) {
        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new RuntimeException("User not found"));

        FantasyTeam fantasyTeam = fantasyTeamRepository.findById(request.getFantasyTeamId())
                .orElseThrow(() -> new RuntimeException("Fantasy team not found"));

        if (!fantasyTeam.getUser().getId().equals(user.getId())) {
            throw new RuntimeException("You can only make transfers for your own team");
        }

        Player playerIn = playerRepository.findById(request.getPlayerInId())
                .orElseThrow(() -> new RuntimeException("Player in not found"));

        Player playerOut = playerRepository.findById(request.getPlayerOutId())
                .orElseThrow(() -> new RuntimeException("Player out not found"));

        // Validate transfer
        validateTransfer(fantasyTeam, playerIn, playerOut);

        GameWeek currentGameWeek = gameWeekRepository.findByIsCurrentTrue()
                .orElseThrow(() -> new RuntimeException("No current game week found"));

        // Check if deadline has passed
        if (currentGameWeek.isDeadlinePassed()) {
            throw new RuntimeException("Transfer deadline has passed for current game week");
        }

        // Calculate transfer cost
        boolean isFreeTransfer = fantasyTeam.getFreeTransfers() > 0;
        int pointsCost = isFreeTransfer ? 0 : 4;

        // Create transfer record
        Transfer transfer = Transfer.builder()
                .user(user)
                .fantasyTeam(fantasyTeam)
                .playerIn(playerIn)
                .playerOut(playerOut)
                .gameWeek(currentGameWeek)
                .transferCost(BigDecimal.ZERO)
                .pointsCost(pointsCost)
                .isFreeTransfer(isFreeTransfer)
                .status(Transfer.TransferStatus.PENDING)
                .build();

        Transfer savedTransfer = transferRepository.save(transfer);

        // Process the transfer
        processTransfer(savedTransfer);

        return savedTransfer;
    }

    @Transactional
    public void processTransfer(Transfer transfer) {
        try {
            FantasyTeam fantasyTeam = transfer.getFantasyTeam();
            Player playerIn = transfer.getPlayerIn();
            Player playerOut = transfer.getPlayerOut();

            // Remove old player
            FantasyPlayer fantasyPlayerOut = fantasyPlayerRepository
                    .findByFantasyTeamIdAndPlayerId(fantasyTeam.getId(), playerOut.getId())
                    .orElseThrow(() -> new RuntimeException("Player out not found in team"));

            // Check if removing captain or vice captain
            boolean wasCaptain = fantasyPlayerOut.getIsCaptain();
            boolean wasViceCaptain = fantasyPlayerOut.getIsViceCaptain();
            boolean wasStarter = fantasyPlayerOut.getIsStarter();

            // Update budget
            fantasyTeam.addBudget(fantasyPlayerOut.getPurchasePrice());
            fantasyTeam.spendBudget(playerIn.getFantasyPrice());

            // Remove old fantasy player
            fantasyPlayerRepository.delete(fantasyPlayerOut);

            // Add new player
            FantasyPlayer fantasyPlayerIn = FantasyPlayer.builder()
                    .fantasyTeam(fantasyTeam)
                    .player(playerIn)
                    .purchasePrice(playerIn.getFantasyPrice())
                    .isCaptain(wasCaptain)
                    .isViceCaptain(wasViceCaptain)
                    .isStarter(wasStarter)
                    .build();

            fantasyPlayerRepository.save(fantasyPlayerIn);

            // Update team transfer stats
            if (transfer.getIsFreeTransfer()) {
                fantasyTeam.setFreeTransfers(fantasyTeam.getFreeTransfers() - 1);
            } else {
                fantasyTeam.setTotalPoints(fantasyTeam.getTotalPoints() - transfer.getPointsCost());
            }
            fantasyTeam.setTotalTransfers(fantasyTeam.getTotalTransfers() + 1);

            fantasyTeamRepository.save(fantasyTeam);

            // Mark transfer as completed
            transfer.markAsProcessed();
            transferRepository.save(transfer);

        } catch (Exception e) {
            transfer.markAsFailed();
            transferRepository.save(transfer);
            throw new RuntimeException("Transfer processing failed: " + e.getMessage());
        }
    }

    private void validateTransfer(FantasyTeam fantasyTeam, Player playerIn, Player playerOut) {
        // Check if player out is in the team
        boolean hasPlayerOut = fantasyPlayerRepository
                .findByFantasyTeamIdAndPlayerId(fantasyTeam.getId(), playerOut.getId())
                .isPresent();

        if (!hasPlayerOut) {
            throw new RuntimeException("Player out is not in your team");
        }

        // Check if player in is already in the team
        boolean hasPlayerIn = fantasyPlayerRepository
                .findByFantasyTeamIdAndPlayerId(fantasyTeam.getId(), playerIn.getId())
                .isPresent();

        if (hasPlayerIn) {
            throw new RuntimeException("Player in is already in your team");
        }

        // Check if players are same position
        if (!playerIn.getPosition().equals(playerOut.getPosition())) {
            throw new RuntimeException("Players must be of the same position");
        }

        // Check if team can afford the transfer
        BigDecimal priceDifference = playerIn.getFantasyPrice().subtract(playerOut.getFantasyPrice());
        if (!fantasyTeam.canAfford(priceDifference)) {
            throw new RuntimeException("Insufficient budget for this transfer");
        }

        // Check if player in is active and not injured
        if (!playerIn.getIsActive()) {
            throw new RuntimeException("Player in is not active");
        }

        if (playerIn.getIsInjured()) {
            throw new RuntimeException("Player in is currently injured");
        }

        // Check team composition after transfer (max 3 from same team)
        long playersFromSameTeam = fantasyTeam.getFantasyPlayers().stream()
                .filter(fp -> !fp.getPlayer().getId().equals(playerOut.getId()))
                .filter(fp -> fp.getPlayer().getTeam().getId().equals(playerIn.getTeam().getId()))
                .count();

        if (playersFromSameTeam >= 3) {
            throw new RuntimeException("Cannot have more than 3 players from the same team");
        }
    }

    public List<Transfer> getUserTransfers(String username) {
        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new RuntimeException("User not found"));

        return transferRepository.findByUserId(user.getId());
    }

    public List<Transfer> getGameWeekTransfers(Long gameWeekId) {
        return transferRepository.findByGameWeekId(gameWeekId);
    }
}
