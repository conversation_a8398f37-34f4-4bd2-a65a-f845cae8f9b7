package Server.Fantasy_Server.repository;

import Server.Fantasy_Server.document.AIRecommendation;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface AIRecommendationRepository extends MongoRepository<AIRecommendation, String> {

    List<AIRecommendation> findByPlayerId(Long playerId);

    List<AIRecommendation> findByGameWeekId(Long gameWeekId);

    List<AIRecommendation> findByRecommendationType(AIRecommendation.RecommendationType type);

    List<AIRecommendation> findBySeason(String season);

    @Query("{ 'gameWeekId': ?0, 'isActive': true }")
    List<AIRecommendation> findActiveRecommendationsByGameWeek(Long gameWeekId);

    @Query("{ 'recommendationType': ?0, 'gameWeekId': ?1, 'isActive': true }")
    List<AIRecommendation> findActiveRecommendationsByTypeAndGameWeek(
            AIRecommendation.RecommendationType type, Long gameWeekId);

    @Query("{ 'confidenceScore': { $gte: ?0 }, 'gameWeekId': ?1, 'isActive': true }")
    List<AIRecommendation> findHighConfidenceRecommendations(BigDecimal minConfidence, Long gameWeekId);

    @Query("{ 'recommendationType': 'PLAYER_OF_THE_WEEK', 'gameWeekId': ?0, 'isActive': true }")
    List<AIRecommendation> findPlayerOfTheWeekRecommendations(Long gameWeekId);

    @Query("{ 'recommendationType': 'CAPTAIN_PICK', 'gameWeekId': ?0, 'isActive': true }")
    List<AIRecommendation> findCaptainPickRecommendations(Long gameWeekId);

    @Query("{ 'recommendationType': { $in: ['TRANSFER_IN', 'TRANSFER_OUT'] }, 'gameWeekId': ?0, 'isActive': true }")
    List<AIRecommendation> findTransferRecommendations(Long gameWeekId);

    @Query("{ 'expiresAt': { $lt: ?0 }, 'isActive': true }")
    List<AIRecommendation> findExpiredRecommendations(LocalDateTime now);

    @Query(value = "{ 'gameWeekId': ?0, 'isActive': true }", sort = "{ 'confidenceScore': -1 }")
    List<AIRecommendation> findTopRecommendationsByGameWeek(Long gameWeekId);

    @Query("{ 'playerId': ?0, 'season': ?1 }")
    List<AIRecommendation> findPlayerSeasonRecommendations(Long playerId, String season);

    @Query("{ 'algorithmVersion': ?0 }")
    List<AIRecommendation> findByAlgorithmVersion(String version);

    @Query("{ 'predictedPoints': { $gte: ?0 }, 'gameWeekId': ?1, 'isActive': true }")
    List<AIRecommendation> findHighScoringPredictions(BigDecimal minPoints, Long gameWeekId);

    @Query("{ 'ownershipPercentage': { $lt: ?0 }, 'gameWeekId': ?1, 'isActive': true }")
    List<AIRecommendation> findDifferentialPicks(BigDecimal maxOwnership, Long gameWeekId);
}
