package Server.Fantasy_Server.service;

import Server.Fantasy_Server.entity.GameWeek;
import Server.Fantasy_Server.repository.GameWeekRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

@Service
@RequiredArgsConstructor
public class GameWeekService {

    private final GameWeekRepository gameWeekRepository;

    public List<GameWeek> getAllGameWeeks() {
        return gameWeekRepository.findAll();
    }

    public GameWeek getCurrentGameWeek() {
        return gameWeekRepository.findByIsCurrentTrue()
                .orElseThrow(() -> new RuntimeException("No current game week found"));
    }

    public GameWeek getGameWeek(Long gameWeekId) {
        return gameWeekRepository.findById(gameWeekId)
                .orElseThrow(() -> new RuntimeException("Game week not found"));
    }

    public List<GameWeek> getGameWeeksBySeason(String season) {
        return gameWeekRepository.findBySeasonOrderByWeekNumber(season);
    }

    public List<GameWeek> getUpcomingGameWeeks() {
        return gameWeekRepository.findUpcomingGameWeeks(LocalDateTime.now());
    }
}
