package Server.Fantasy_Server.controller;

import Server.Fantasy_Server.dto.LeaderboardDto;
import Server.Fantasy_Server.entity.Leaderboard;
import Server.Fantasy_Server.service.LeaderboardService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/leaderboard")
@RequiredArgsConstructor
@SecurityRequirement(name = "bearerAuth")
@Tag(name = "Leaderboard", description = "Leaderboard and ranking endpoints")
public class LeaderboardController {

    private final LeaderboardService leaderboardService;

    @GetMapping("/overall")
    @Operation(summary = "Get overall leaderboard", description = "Get overall season leaderboard")
    public ResponseEntity<Page<LeaderboardDto>> getOverallLeaderboard(Pageable pageable) {
        Page<LeaderboardDto> leaderboard = leaderboardService.getOverallLeaderboard(pageable);
        return ResponseEntity.ok(leaderboard);
    }

    @GetMapping("/gameweek/{gameWeekId}")
    @Operation(summary = "Get gameweek leaderboard", description = "Get leaderboard for specific gameweek")
    public ResponseEntity<Page<LeaderboardDto>> getGameWeekLeaderboard(
            @PathVariable Long gameWeekId,
            Pageable pageable
    ) {
        Page<LeaderboardDto> leaderboard = leaderboardService.getGameWeekLeaderboard(gameWeekId, pageable);
        return ResponseEntity.ok(leaderboard);
    }

    @GetMapping("/monthly")
    @Operation(summary = "Get monthly leaderboard", description = "Get monthly leaderboard")
    public ResponseEntity<Page<LeaderboardDto>> getMonthlyLeaderboard(Pageable pageable) {
        Page<LeaderboardDto> leaderboard = leaderboardService.getMonthlyLeaderboard(pageable);
        return ResponseEntity.ok(leaderboard);
    }

    @PostMapping("/update")
    @Operation(summary = "Update leaderboards", description = "Manually trigger leaderboard update")
    public ResponseEntity<Void> updateLeaderboards() {
        leaderboardService.updateAllLeaderboards();
        return ResponseEntity.ok().build();
    }
}
