package Server.Fantasy_Server.document;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@Document(collection = "player_predictions")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PlayerPrediction {

    @Id
    private String id;

    @Field("player_id")
    private Long playerId;

    @Field("fixture_id")
    private Long fixtureId;

    @Field("game_week_id")
    private Long gameWeekId;

    @Field("season")
    private String season;

    // Predicted stats
    @Field("predicted_minutes")
    private BigDecimal predictedMinutes;

    @Field("predicted_goals")
    private BigDecimal predictedGoals;

    @Field("predicted_assists")
    private BigDecimal predictedAssists;

    @Field("predicted_clean_sheet_probability")
    private BigDecimal predictedCleanSheetProbability;

    @Field("predicted_yellow_card_probability")
    private BigDecimal predictedYellowCardProbability;

    @Field("predicted_red_card_probability")
    private BigDecimal predictedRedCardProbability;

    @Field("predicted_fantasy_points")
    private BigDecimal predictedFantasyPoints;

    @Field("confidence_interval_lower")
    private BigDecimal confidenceIntervalLower;

    @Field("confidence_interval_upper")
    private BigDecimal confidenceIntervalUpper;

    // Model information
    @Field("model_version")
    private String modelVersion;

    @Field("prediction_algorithm")
    private String predictionAlgorithm;

    @Field("feature_importance")
    private Map<String, BigDecimal> featureImportance;

    // Input factors
    @Field("recent_form")
    private List<Integer> recentForm;

    @Field("home_away_factor")
    private String homeAwayFactor;

    @Field("opposition_strength")
    private BigDecimal oppositionStrength;

    @Field("player_fitness_score")
    private BigDecimal playerFitnessScore;

    @Field("team_form")
    private BigDecimal teamForm;

    @Field("historical_vs_opposition")
    private Map<String, Object> historicalVsOpposition;

    @Field("weather_conditions")
    private Map<String, Object> weatherConditions;

    @Field("injury_risk_factors")
    private List<String> injuryRiskFactors;

    // Accuracy tracking
    @Field("actual_fantasy_points")
    private Integer actualFantasyPoints;

    @Field("prediction_accuracy")
    private BigDecimal predictionAccuracy;

    @Field("absolute_error")
    private BigDecimal absoluteError;

    @Field("is_evaluated")
    private Boolean isEvaluated = false;

    // Metadata
    @Field("created_at")
    private LocalDateTime createdAt;

    @Field("prediction_date")
    private LocalDateTime predictionDate;

    @Field("match_date")
    private LocalDateTime matchDate;

    public void evaluatePrediction(Integer actualPoints) {
        this.actualFantasyPoints = actualPoints;
        
        if (predictedFantasyPoints != null && actualPoints != null) {
            BigDecimal predicted = predictedFantasyPoints;
            BigDecimal actual = new BigDecimal(actualPoints);
            
            // Calculate absolute error
            this.absoluteError = predicted.subtract(actual).abs();
            
            // Calculate accuracy (1 - normalized error)
            BigDecimal maxError = actual.max(predicted).max(BigDecimal.ONE);
            this.predictionAccuracy = BigDecimal.ONE.subtract(absoluteError.divide(maxError, 4, BigDecimal.ROUND_HALF_UP));
        }
        
        this.isEvaluated = true;
    }

    public boolean isHighAccuracy() {
        return predictionAccuracy != null && predictionAccuracy.compareTo(new BigDecimal("0.8")) >= 0;
    }

    public boolean isWithinConfidenceInterval(Integer actualPoints) {
        if (actualPoints == null || confidenceIntervalLower == null || confidenceIntervalUpper == null) {
            return false;
        }
        
        BigDecimal actual = new BigDecimal(actualPoints);
        return actual.compareTo(confidenceIntervalLower) >= 0 && 
               actual.compareTo(confidenceIntervalUpper) <= 0;
    }
}
