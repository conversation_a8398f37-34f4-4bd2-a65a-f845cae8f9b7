package Server.Fantasy_Server.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Entity
@Table(name = "fantasy_teams")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FantasyTeam {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(nullable = false)
    @NotBlank(message = "Team name is required")
    private String name;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false)
    @NotNull(message = "User is required")
    private User user;

    @Column(name = "total_budget", precision = 15, scale = 2, nullable = false)
    private BigDecimal totalBudget;

    @Column(name = "remaining_budget", precision = 15, scale = 2, nullable = false)
    private BigDecimal remainingBudget;

    @Column(name = "total_points")
    private Integer totalPoints = 0;

    @Column(name = "gameweek_points")
    private Integer gameweekPoints = 0;

    @Column(name = "free_transfers")
    private Integer freeTransfers = 1;

    @Column(name = "total_transfers")
    private Integer totalTransfers = 0;

    @Column(name = "is_active")
    private Boolean isActive = true;

    @Column(name = "created_at")
    private LocalDateTime createdAt;

    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    @OneToMany(mappedBy = "fantasyTeam", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<FantasyPlayer> fantasyPlayers;

    @OneToMany(mappedBy = "fantasyTeam", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<Transfer> transfers;

    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
        if (totalBudget == null) {
            totalBudget = new BigDecimal("100000000"); // 100 million default budget
        }
        if (remainingBudget == null) {
            remainingBudget = totalBudget;
        }
    }

    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }

    public boolean canAfford(BigDecimal amount) {
        return remainingBudget.compareTo(amount) >= 0;
    }

    public void spendBudget(BigDecimal amount) {
        remainingBudget = remainingBudget.subtract(amount);
    }

    public void addBudget(BigDecimal amount) {
        remainingBudget = remainingBudget.add(amount);
    }

    public int getPlayerCount() {
        return fantasyPlayers != null ? fantasyPlayers.size() : 0;
    }

    public boolean isTeamComplete() {
        return getPlayerCount() == 15;
    }
}
