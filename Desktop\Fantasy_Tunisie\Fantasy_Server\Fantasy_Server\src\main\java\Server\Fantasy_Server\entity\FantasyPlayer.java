package Server.Fantasy_Server.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Entity
@Table(name = "fantasy_players", 
       uniqueConstraints = @UniqueConstraint(columnNames = {"fantasy_team_id", "player_id"}))
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FantasyPlayer {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "fantasy_team_id", nullable = false)
    @NotNull(message = "Fantasy team is required")
    private FantasyTeam fantasyTeam;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "player_id", nullable = false)
    @NotNull(message = "Player is required")
    private Player player;

    @Column(name = "purchase_price", precision = 15, scale = 2, nullable = false)
    @NotNull(message = "Purchase price is required")
    private BigDecimal purchasePrice;

    @Column(name = "is_captain")
    private Boolean isCaptain = false;

    @Column(name = "is_vice_captain")
    private Boolean isViceCaptain = false;

    @Column(name = "is_starter")
    private Boolean isStarter = true;

    @Column(name = "gameweek_points")
    private Integer gameweekPoints = 0;

    @Column(name = "total_points")
    private Integer totalPoints = 0;

    @Column(name = "created_at")
    private LocalDateTime createdAt;

    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }

    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }

    public void addPoints(int points) {
        gameweekPoints += points;
        totalPoints += points;
    }

    public void resetGameweekPoints() {
        gameweekPoints = 0;
    }

    public int getEffectivePoints() {
        int points = gameweekPoints;
        if (isCaptain) {
            points *= 2; // Captain gets double points
        } else if (isViceCaptain && !hasActiveCaptain()) {
            points *= 2; // Vice captain gets double points if captain doesn't play
        }
        return points;
    }

    private boolean hasActiveCaptain() {
        // This would need to be implemented based on whether the captain played
        // For now, assume captain is always active
        return true;
    }
}
