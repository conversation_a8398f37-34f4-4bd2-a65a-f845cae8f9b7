package Server.Fantasy_Server.document;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.time.LocalDateTime;
import java.util.Map;

@Document(collection = "audit_logs")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AuditLog {

    @Id
    private String id;

    @Field("user_id")
    private Long userId;

    @Field("username")
    private String username;

    @Field("action")
    private String action;

    @Field("entity_type")
    private String entityType;

    @Field("entity_id")
    private String entityId;

    @Field("old_values")
    private Map<String, Object> oldValues;

    @Field("new_values")
    private Map<String, Object> newValues;

    @Field("ip_address")
    private String ipAddress;

    @Field("user_agent")
    private String userAgent;

    @Field("session_id")
    private String sessionId;

    @Field("request_id")
    private String requestId;

    @Field("endpoint")
    private String endpoint;

    @Field("http_method")
    private String httpMethod;

    @Field("status_code")
    private Integer statusCode;

    @Field("execution_time_ms")
    private Long executionTimeMs;

    @Field("error_message")
    private String errorMessage;

    @Field("additional_data")
    private Map<String, Object> additionalData;

    @Field("timestamp")
    private LocalDateTime timestamp;

    public static AuditLog createUserAction(Long userId, String username, String action, 
                                          String entityType, String entityId) {
        return AuditLog.builder()
                .userId(userId)
                .username(username)
                .action(action)
                .entityType(entityType)
                .entityId(entityId)
                .timestamp(LocalDateTime.now())
                .build();
    }

    public static AuditLog createSystemAction(String action, String entityType, String entityId) {
        return AuditLog.builder()
                .action(action)
                .entityType(entityType)
                .entityId(entityId)
                .timestamp(LocalDateTime.now())
                .build();
    }

    public static AuditLog createApiCall(String endpoint, String httpMethod, 
                                       Integer statusCode, Long executionTime) {
        return AuditLog.builder()
                .action("API_CALL")
                .endpoint(endpoint)
                .httpMethod(httpMethod)
                .statusCode(statusCode)
                .executionTimeMs(executionTime)
                .timestamp(LocalDateTime.now())
                .build();
    }
}
