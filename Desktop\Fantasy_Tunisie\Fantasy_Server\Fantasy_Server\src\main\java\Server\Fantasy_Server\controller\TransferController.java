package Server.Fantasy_Server.controller;

import Server.Fantasy_Server.dto.TransferRequest;
import Server.Fantasy_Server.entity.Transfer;
import Server.Fantasy_Server.service.TransferService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/transfers")
@RequiredArgsConstructor
@SecurityRequirement(name = "bearerAuth")
@Tag(name = "Transfers", description = "Player transfer management endpoints")
public class TransferController {

    private final TransferService transferService;

    @PostMapping
    @Operation(summary = "Make a transfer", description = "Transfer a player in/out of the fantasy team")
    public ResponseEntity<Transfer> makeTransfer(
            @Valid @RequestBody TransferRequest request,
            Authentication authentication
    ) {
        Transfer transfer = transferService.makeTransfer(request, authentication.getName());
        return ResponseEntity.status(HttpStatus.CREATED).body(transfer);
    }

    @GetMapping("/my-transfers")
    @Operation(summary = "Get user transfers", description = "Get all transfers made by the authenticated user")
    public ResponseEntity<List<Transfer>> getMyTransfers(Authentication authentication) {
        List<Transfer> transfers = transferService.getUserTransfers(authentication.getName());
        return ResponseEntity.ok(transfers);
    }

    @GetMapping("/gameweek/{gameWeekId}")
    @Operation(summary = "Get gameweek transfers", description = "Get all transfers for a specific gameweek")
    public ResponseEntity<List<Transfer>> getGameWeekTransfers(@PathVariable Long gameWeekId) {
        List<Transfer> transfers = transferService.getGameWeekTransfers(gameWeekId);
        return ResponseEntity.ok(transfers);
    }
}
