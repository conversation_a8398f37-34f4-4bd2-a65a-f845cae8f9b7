package Server.Fantasy_Server.service;

import Server.Fantasy_Server.dto.*;
import Server.Fantasy_Server.entity.*;
import Server.Fantasy_Server.repository.*;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class FantasyTeamService {

    private final FantasyTeamRepository fantasyTeamRepository;
    private final PlayerRepository playerRepository;
    private final UserRepository userRepository;
    private final FantasyPlayerRepository fantasyPlayerRepository;
    private final GameWeekRepository gameWeekRepository;

    @Transactional
    public FantasyTeamDto createFantasyTeam(CreateFantasyTeamRequest request, String username) {
        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new RuntimeException("User not found"));

        // Check if user already has an active fantasy team
        if (fantasyTeamRepository.findByUserIdAndIsActiveTrue(user.getId()).isPresent()) {
            throw new RuntimeException("User already has an active fantasy team");
        }

        // Validate team composition
        validateTeamComposition(request.getPlayerIds());

        // Calculate total cost
        List<Player> players = playerRepository.findAllById(request.getPlayerIds());
        BigDecimal totalCost = players.stream()
                .map(Player::getFantasyPrice)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal budget = new BigDecimal("100000000"); // 100 million
        if (totalCost.compareTo(budget) > 0) {
            throw new RuntimeException("Team exceeds budget limit");
        }

        // Create fantasy team
        FantasyTeam fantasyTeam = FantasyTeam.builder()
                .name(request.getName())
                .user(user)
                .totalBudget(budget)
                .remainingBudget(budget.subtract(totalCost))
                .build();

        FantasyTeam savedTeam = fantasyTeamRepository.save(fantasyTeam);

        // Create fantasy players
        for (Player player : players) {
            FantasyPlayer fantasyPlayer = FantasyPlayer.builder()
                    .fantasyTeam(savedTeam)
                    .player(player)
                    .purchasePrice(player.getFantasyPrice())
                    .isCaptain(player.getId().equals(request.getCaptainId()))
                    .isViceCaptain(player.getId().equals(request.getViceCaptainId()))
                    .isStarter(request.getStartingLineup().contains(player.getId()))
                    .build();

            fantasyPlayerRepository.save(fantasyPlayer);
        }

        return mapToFantasyTeamDto(savedTeam);
    }

    public FantasyTeamDto getFantasyTeam(Long teamId) {
        FantasyTeam team = fantasyTeamRepository.findById(teamId)
                .orElseThrow(() -> new RuntimeException("Fantasy team not found"));
        return mapToFantasyTeamDto(team);
    }

    public FantasyTeamDto getUserFantasyTeam(String username) {
        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new RuntimeException("User not found"));

        FantasyTeam team = fantasyTeamRepository.findByUserIdAndIsActiveTrue(user.getId())
                .orElseThrow(() -> new RuntimeException("User has no active fantasy team"));

        return mapToFantasyTeamDto(team);
    }

    public List<FantasyTeamDto> getAllFantasyTeams() {
        return fantasyTeamRepository.findByIsActiveTrue().stream()
                .map(this::mapToFantasyTeamDto)
                .collect(Collectors.toList());
    }

    @Transactional
    public FantasyTeamDto updateCaptain(Long teamId, Long captainId, Long viceCaptainId, String username) {
        FantasyTeam team = getTeamByIdAndUser(teamId, username);

        // Reset current captain and vice captain
        team.getFantasyPlayers().forEach(fp -> {
            fp.setIsCaptain(false);
            fp.setIsViceCaptain(false);
        });

        // Set new captain and vice captain
        team.getFantasyPlayers().forEach(fp -> {
            if (fp.getPlayer().getId().equals(captainId)) {
                fp.setIsCaptain(true);
            }
            if (fp.getPlayer().getId().equals(viceCaptainId)) {
                fp.setIsViceCaptain(true);
            }
        });

        fantasyTeamRepository.save(team);
        return mapToFantasyTeamDto(team);
    }

    @Transactional
    public FantasyTeamDto updateStartingLineup(Long teamId, List<Long> startingLineup, String username) {
        if (startingLineup.size() != 11) {
            throw new RuntimeException("Starting lineup must have exactly 11 players");
        }

        FantasyTeam team = getTeamByIdAndUser(teamId, username);

        // Update starting lineup
        team.getFantasyPlayers().forEach(fp -> {
            fp.setIsStarter(startingLineup.contains(fp.getPlayer().getId()));
        });

        fantasyTeamRepository.save(team);
        return mapToFantasyTeamDto(team);
    }

    private void validateTeamComposition(List<Long> playerIds) {
        if (playerIds.size() != 15) {
            throw new RuntimeException("Team must have exactly 15 players");
        }

        List<Player> players = playerRepository.findAllById(playerIds);
        if (players.size() != 15) {
            throw new RuntimeException("Some players not found");
        }

        // Count players by position
        long goalkeepers = players.stream().filter(p -> p.getPosition() == Player.Position.GOALKEEPER).count();
        long defenders = players.stream().filter(p -> p.getPosition() == Player.Position.DEFENDER).count();
        long midfielders = players.stream().filter(p -> p.getPosition() == Player.Position.MIDFIELDER).count();
        long forwards = players.stream().filter(p -> p.getPosition() == Player.Position.FORWARD).count();

        if (goalkeepers != 2) {
            throw new RuntimeException("Team must have exactly 2 goalkeepers");
        }
        if (defenders != 5) {
            throw new RuntimeException("Team must have exactly 5 defenders");
        }
        if (midfielders != 5) {
            throw new RuntimeException("Team must have exactly 5 midfielders");
        }
        if (forwards != 3) {
            throw new RuntimeException("Team must have exactly 3 forwards");
        }

        // Check max 3 players from same team
        players.stream()
                .collect(Collectors.groupingBy(p -> p.getTeam().getId(), Collectors.counting()))
                .values()
                .forEach(count -> {
                    if (count > 3) {
                        throw new RuntimeException("Cannot have more than 3 players from the same team");
                    }
                });
    }

    private FantasyTeam getTeamByIdAndUser(Long teamId, String username) {
        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new RuntimeException("User not found"));

        FantasyTeam team = fantasyTeamRepository.findById(teamId)
                .orElseThrow(() -> new RuntimeException("Fantasy team not found"));

        if (!team.getUser().getId().equals(user.getId())) {
            throw new RuntimeException("You can only modify your own team");
        }

        return team;
    }

    private FantasyTeamDto mapToFantasyTeamDto(FantasyTeam team) {
        return FantasyTeamDto.builder()
                .id(team.getId())
                .name(team.getName())
                .userId(team.getUser().getId())
                .username(team.getUser().getUsername())
                .totalBudget(team.getTotalBudget())
                .remainingBudget(team.getRemainingBudget())
                .totalPoints(team.getTotalPoints())
                .gameweekPoints(team.getGameweekPoints())
                .freeTransfers(team.getFreeTransfers())
                .totalTransfers(team.getTotalTransfers())
                .isActive(team.getIsActive())
                .createdAt(team.getCreatedAt())
                .playerCount(team.getPlayerCount())
                .isTeamComplete(team.isTeamComplete())
                .build();
    }
}
