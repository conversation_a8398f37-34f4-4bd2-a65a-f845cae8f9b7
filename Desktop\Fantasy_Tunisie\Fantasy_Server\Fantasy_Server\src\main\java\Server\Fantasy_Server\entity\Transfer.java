package Server.Fantasy_Server.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Entity
@Table(name = "transfers")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Transfer {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false)
    @NotNull(message = "User is required")
    private User user;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "fantasy_team_id", nullable = false)
    @NotNull(message = "Fantasy team is required")
    private FantasyTeam fantasyTeam;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "player_in_id", nullable = false)
    @NotNull(message = "Player in is required")
    private Player playerIn;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "player_out_id", nullable = false)
    @NotNull(message = "Player out is required")
    private Player playerOut;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "game_week_id", nullable = false)
    @NotNull(message = "Game week is required")
    private GameWeek gameWeek;

    @Column(name = "transfer_cost", precision = 15, scale = 2)
    private BigDecimal transferCost = BigDecimal.ZERO;

    @Column(name = "points_cost")
    private Integer pointsCost = 0;

    @Column(name = "is_free_transfer")
    private Boolean isFreeTransfer = false;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private TransferStatus status = TransferStatus.PENDING;

    @Column(name = "created_at")
    private LocalDateTime createdAt;

    @Column(name = "processed_at")
    private LocalDateTime processedAt;

    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
    }

    public void markAsProcessed() {
        status = TransferStatus.COMPLETED;
        processedAt = LocalDateTime.now();
    }

    public void markAsFailed() {
        status = TransferStatus.FAILED;
        processedAt = LocalDateTime.now();
    }

    public boolean isPending() {
        return status == TransferStatus.PENDING;
    }

    public boolean isCompleted() {
        return status == TransferStatus.COMPLETED;
    }

    public enum TransferStatus {
        PENDING,
        COMPLETED,
        FAILED,
        CANCELLED
    }
}
