package Server.Fantasy_Server.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Entity
@Table(name = "leaderboards")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Leaderboard {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false)
    @NotNull(message = "User is required")
    private User user;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "fantasy_team_id", nullable = false)
    @NotNull(message = "Fantasy team is required")
    private FantasyTeam fantasyTeam;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "game_week_id")
    private GameWeek gameWeek; // null for overall leaderboard

    @Column(name = "total_points", nullable = false)
    private Integer totalPoints = 0;

    @Column(name = "gameweek_points")
    private Integer gameweekPoints = 0;

    @Column(name = "overall_rank")
    private Integer overallRank;

    @Column(name = "gameweek_rank")
    private Integer gameweekRank;

    @Column(name = "previous_rank")
    private Integer previousRank;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private LeaderboardType type = LeaderboardType.OVERALL;

    @Column(name = "created_at")
    private LocalDateTime createdAt;

    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }

    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }

    public Integer getRankChange() {
        if (previousRank == null || overallRank == null) {
            return null;
        }
        return previousRank - overallRank; // Positive means rank improved
    }

    public enum LeaderboardType {
        OVERALL,
        GAMEWEEK,
        MONTHLY
    }
}
