package Server.Fantasy_Server.document;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.time.LocalDateTime;
import java.util.Map;

@Document(collection = "player_stats")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PlayerStat {

    @Id
    private String id;

    @Field("player_id")
    private Long playerId;

    @Field("fixture_id")
    private Long fixtureId;

    @Field("team_id")
    private Long teamId;

    @Field("game_week_id")
    private Long gameWeekId;

    @Field("season")
    private String season;

    // Basic stats
    @Field("minutes_played")
    private Integer minutesPlayed = 0;

    @Field("goals")
    private Integer goals = 0;

    @Field("assists")
    private Integer assists = 0;

    @Field("yellow_cards")
    private Integer yellowCards = 0;

    @Field("red_cards")
    private Integer redCards = 0;

    @Field("own_goals")
    private Integer ownGoals = 0;

    @Field("penalties_scored")
    private Integer penaltiesScored = 0;

    @Field("penalties_missed")
    private Integer penaltiesMissed = 0;

    @Field("penalties_saved")
    private Integer penaltiesSaved = 0;

    @Field("clean_sheet")
    private Boolean cleanSheet = false;

    // Advanced stats
    @Field("shots")
    private Integer shots = 0;

    @Field("shots_on_target")
    private Integer shotsOnTarget = 0;

    @Field("passes")
    private Integer passes = 0;

    @Field("passes_completed")
    private Integer passesCompleted = 0;

    @Field("tackles")
    private Integer tackles = 0;

    @Field("tackles_won")
    private Integer tacklesWon = 0;

    @Field("interceptions")
    private Integer interceptions = 0;

    @Field("clearances")
    private Integer clearances = 0;

    @Field("blocks")
    private Integer blocks = 0;

    @Field("saves")
    private Integer saves = 0;

    @Field("goals_conceded")
    private Integer goalsConceded = 0;

    @Field("fouls_committed")
    private Integer foulsCommitted = 0;

    @Field("fouls_drawn")
    private Integer foulsDrawn = 0;

    @Field("offsides")
    private Integer offsides = 0;

    // Fantasy specific
    @Field("fantasy_points")
    private Integer fantasyPoints = 0;

    @Field("bonus_points")
    private Integer bonusPoints = 0;

    @Field("captain_points")
    private Integer captainPoints = 0;

    @Field("vice_captain_points")
    private Integer viceCaptainPoints = 0;

    // Synergy bonuses
    @Field("same_team_goal_assist_bonus")
    private Integer sameTeamGoalAssistBonus = 0;

    @Field("same_team_clean_sheet_bonus")
    private Integer sameTeamCleanSheetBonus = 0;

    // Additional flexible stats
    @Field("additional_stats")
    private Map<String, Object> additionalStats;

    @Field("created_at")
    private LocalDateTime createdAt;

    @Field("updated_at")
    private LocalDateTime updatedAt;

    public void calculateFantasyPoints() {
        int points = 0;

        // Basic points for playing
        if (minutesPlayed > 0) {
            points += 1;
        }
        if (minutesPlayed >= 60) {
            points += 1; // Additional point for playing 60+ minutes
        }

        // Goals (position-based scoring)
        points += goals * getGoalPoints();

        // Assists
        points += assists * 3;

        // Clean sheet (GK and DEF only)
        if (cleanSheet && isDefensivePosition()) {
            points += 4;
        }

        // Cards
        points -= yellowCards * 1;
        points -= redCards * 3;

        // Own goals
        points -= ownGoals * 2;

        // Penalties
        points += penaltiesScored * getGoalPoints();
        points -= penaltiesMissed * 2;
        points += penaltiesSaved * 5;

        // Bonus points
        points += bonusPoints;
        points += sameTeamGoalAssistBonus;
        points += sameTeamCleanSheetBonus;

        this.fantasyPoints = points;
    }

    private int getGoalPoints() {
        // This would need player position info - for now return default
        return 4; // Forward default
    }

    private boolean isDefensivePosition() {
        // This would need player position info - for now return false
        return false;
    }

    public void addBonusPoints(int bonus) {
        this.bonusPoints += bonus;
        calculateFantasyPoints();
    }
}
